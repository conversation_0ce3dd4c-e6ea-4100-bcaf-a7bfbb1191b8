#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
临时禁用token验证的测试脚本
通过修改配置来测试接口功能
"""

import requests
import json
import os
import time
import yaml
from datetime import datetime

# 禁用代理
os.environ['no_proxy'] = '127.0.0.1,localhost'
proxies = {'http': None, 'https': None}

def backup_and_modify_config():
    """备份并修改配置文件以禁用token验证"""
    config_path = "resource/config/zxtech_config_dev.yaml"
    backup_path = "resource/config/zxtech_config_dev.yaml.backup"
    
    try:
        # 备份原配置
        with open(config_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        with open(backup_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        # 读取配置
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        # 修改token验证设置
        if 'identify' in config:
            config['identify']['enable'] = False
            print("✓ 已禁用token验证")
        
        # 写回配置
        with open(config_path, 'w', encoding='utf-8') as f:
            yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
        
        return True
        
    except Exception as e:
        print(f"✗ 修改配置失败: {str(e)}")
        return False

def restore_config():
    """恢复原配置文件"""
    config_path = "resource/config/zxtech_config_dev.yaml"
    backup_path = "resource/config/zxtech_config_dev.yaml.backup"
    
    try:
        if os.path.exists(backup_path):
            with open(backup_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            with open(config_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            os.remove(backup_path)
            print("✓ 已恢复原配置")
            return True
    except Exception as e:
        print(f"✗ 恢复配置失败: {str(e)}")
        return False

def test_faq_api():
    """测试问答接口"""
    print("\n" + "="*50)
    print("测试问答接口（无token验证）")
    print("="*50)
    
    url = "http://127.0.0.1:7567/zte-ibo-acm-productretrieve/faq"
    headers = {
        'X-Emp-No': '0668001399',
        'Content-Type': 'application/json'
    }
    
    test_cases = [
        {
            "name": "基础问答测试",
            "data": {"text": "M6000-8S支持哪些接口"}
        },
        {
            "name": "简单问题测试", 
            "data": {"text": "什么是路由器"}
        },
        {
            "name": "产品查询测试",
            "data": {"text": "ZTE产品有哪些"}
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{i}. {test_case['name']}")
        print(f"问题: {test_case['data']['text']}")
        
        try:
            start_time = time.time()
            response = requests.post(url, headers=headers, json=test_case['data'], 
                                   proxies=proxies, timeout=60)
            end_time = time.time()
            
            print(f"响应时间: {(end_time - start_time)*1000:.2f}ms")
            print(f"状态码: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    json_response = response.json()
                    print("✓ 请求成功")
                    print("响应内容:")
                    print(json.dumps(json_response, indent=2, ensure_ascii=False))
                except json.JSONDecodeError:
                    print("✗ 响应不是有效的JSON格式")
                    print(f"原始响应: {response.text}")
            else:
                print(f"✗ 请求失败，状态码: {response.status_code}")
                print(f"错误响应: {response.text}")
                
        except requests.exceptions.Timeout:
            print("✗ 请求超时")
        except requests.exceptions.ConnectionError:
            print("✗ 连接失败")
        except Exception as e:
            print(f"✗ 请求异常: {str(e)}")

def test_error_cases():
    """测试错误情况"""
    print("\n" + "="*50)
    print("测试错误情况")
    print("="*50)
    
    url = "http://127.0.0.1:7567/zte-ibo-acm-productretrieve/faq"
    
    # 测试空输入
    print("\n1. 测试空输入:")
    try:
        response = requests.post(
            url,
            headers={
                'X-Emp-No': '0668001399',
                'Content-Type': 'application/json'
            },
            json={"text": ""},
            proxies=proxies,
            timeout=10
        )
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            try:
                json_response = response.json()
                print("响应内容:")
                print(json.dumps(json_response, indent=2, ensure_ascii=False))
            except:
                print(f"响应: {response.text}")
        else:
            print(f"错误响应: {response.text}")
    except Exception as e:
        print(f"异常: {str(e)}")
    
    # 测试缺少X-Emp-No
    print("\n2. 测试缺少X-Emp-No头部:")
    try:
        response = requests.post(
            url,
            headers={'Content-Type': 'application/json'},
            json={"text": "测试问题"},
            proxies=proxies,
            timeout=10
        )
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            try:
                json_response = response.json()
                print("响应内容:")
                print(json.dumps(json_response, indent=2, ensure_ascii=False))
            except:
                print(f"响应: {response.text}")
        else:
            print(f"错误响应: {response.text}")
    except Exception as e:
        print(f"异常: {str(e)}")

def main():
    """主测试函数"""
    print(f"开始无token验证接口测试 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 检查服务器是否运行
    try:
        response = requests.get("http://127.0.0.1:7567/zte-ibo-acm-productretrieve/info", 
                              proxies=proxies, timeout=5)
        if response.status_code != 200:
            print("✗ 服务器未正常运行")
            return
    except:
        print("✗ 无法连接到服务器，请确认服务是否启动")
        return
    
    print("✓ 服务器正在运行")
    
    # 备份并修改配置
    if backup_and_modify_config():
        print("请重启服务器以使配置生效，然后按Enter继续...")
        input()
        
        try:
            # 执行测试
            test_faq_api()
            test_error_cases()
        finally:
            # 恢复配置
            restore_config()
            print("\n请重启服务器以恢复原配置")
    
    print(f"\n测试结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
