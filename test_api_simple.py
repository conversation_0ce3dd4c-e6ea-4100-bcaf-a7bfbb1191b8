#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json
import os
import time
from datetime import datetime

# 禁用代理
os.environ['no_proxy'] = '127.0.0.1,localhost'
proxies = {'http': None, 'https': None}

def print_separator(title=""):
    """打印分隔线"""
    if title:
        print(f"\n{'='*20} {title} {'='*20}")
    else:
        print("="*60)

def test_health_check():
    """测试健康检查接口"""
    print_separator("健康检查接口测试")
    
    try:
        start_time = time.time()
        response = requests.get("http://127.0.0.1:7567/zte-ibo-acm-productretrieve/info", 
                              proxies=proxies, timeout=10)
        end_time = time.time()
        
        print(f"请求URL: http://127.0.0.1:7567/zte-ibo-acm-productretrieve/info")
        print(f"响应时间: {(end_time - start_time)*1000:.2f}ms")
        print(f"状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            try:
                json_response = response.json()
                if json_response.get("status") == "ok":
                    print("✓ 健康检查接口正常")
                else:
                    print("✗ 健康检查接口响应异常")
            except json.JSONDecodeError:
                print("✗ 响应不是有效的JSON格式")
        else:
            print(f"✗ 健康检查接口失败，状态码: {response.status_code}")
            
    except requests.exceptions.Timeout:
        print("✗ 健康检查接口超时")
    except requests.exceptions.ConnectionError:
        print("✗ 无法连接到服务器，请确认服务是否启动")
    except Exception as e:
        print(f"✗ 健康检查接口异常: {str(e)}")

def test_info_post():
    """测试健康检查POST方法"""
    print_separator("健康检查POST方法测试")
    
    try:
        start_time = time.time()
        response = requests.post("http://127.0.0.1:7567/zte-ibo-acm-productretrieve/info", 
                               proxies=proxies, timeout=10)
        end_time = time.time()
        
        print(f"请求URL: http://127.0.0.1:7567/zte-ibo-acm-productretrieve/info (POST)")
        print(f"响应时间: {(end_time - start_time)*1000:.2f}ms")
        print(f"状态码: {response.status_code}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            print("✓ 健康检查POST方法正常")
        else:
            print(f"✗ 健康检查POST方法失败，状态码: {response.status_code}")
            
    except Exception as e:
        print(f"✗ 健康检查POST方法异常: {str(e)}")

def check_server_running():
    """检查服务器是否运行"""
    print_separator("服务器状态检查")
    
    try:
        response = requests.get("http://127.0.0.1:7567/zte-ibo-acm-productretrieve/info", 
                              proxies=proxies, timeout=5)
        if response.status_code == 200:
            print("✓ 服务器正在运行")
            return True
        else:
            print(f"✗ 服务器响应异常，状态码: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("✗ 无法连接到服务器，服务可能未启动")
        return False
    except Exception as e:
        print(f"✗ 服务器检查异常: {str(e)}")
        return False

def main():
    """主测试函数"""
    print(f"开始简单API接口测试 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print_separator()
    
    # 检查服务器是否运行
    if check_server_running():
        # 基础功能测试
        test_health_check()
        test_info_post()
    else:
        print("请先启动服务器后再进行测试")
    
    print_separator("测试完成")
    print(f"测试结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
