#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试FAQ接口（已将/faq加入token验证排除列表）
需要重启服务器后运行此测试
"""

import requests
import json
import os
import time
from datetime import datetime

# 禁用代理
os.environ['no_proxy'] = '127.0.0.1,localhost'
proxies = {'http': None, 'https': None}

def print_separator(title=""):
    """打印分隔线"""
    if title:
        print(f"\n{'='*20} {title} {'='*20}")
    else:
        print("="*60)

def test_faq_api():
    """测试问答接口"""
    print_separator("问答接口测试（无token验证）")
    
    url = "http://127.0.0.1:7567/zte-ibo-acm-productretrieve/faq"
    headers = {
        'X-Emp-No': '0668001399',
        'Content-Type': 'application/json'
    }
    
    test_cases = [
        {
            "name": "基础问答测试",
            "data": {"text": "M6000-8S支持哪些接口"},
            "description": "测试产品相关问题"
        },
        {
            "name": "简单问题测试", 
            "data": {"text": "什么是路由器"},
            "description": "测试通用技术问题"
        },
        {
            "name": "产品查询测试",
            "data": {"text": "ZTE产品有哪些"},
            "description": "测试产品查询功能"
        },
        {
            "name": "技术规格测试",
            "data": {"text": "5G基站的技术参数"},
            "description": "测试技术规格查询"
        }
    ]
    
    success_count = 0
    total_count = len(test_cases)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{i}. {test_case['name']}")
        print(f"描述: {test_case['description']}")
        print(f"问题: {test_case['data']['text']}")
        print(f"请求URL: {url}")
        print(f"请求头: {json.dumps(headers, ensure_ascii=False)}")
        print(f"请求体: {json.dumps(test_case['data'], ensure_ascii=False)}")
        
        try:
            start_time = time.time()
            response = requests.post(url, headers=headers, json=test_case['data'], 
                                   proxies=proxies, timeout=60)
            end_time = time.time()
            
            response_time = (end_time - start_time) * 1000
            print(f"响应时间: {response_time:.2f}ms")
            print(f"状态码: {response.status_code}")
            print(f"响应头: {dict(response.headers)}")
            
            if response.status_code == 200:
                try:
                    json_response = response.json()
                    print("✓ 请求成功")
                    success_count += 1
                    
                    # 格式化输出响应内容
                    print("响应内容:")
                    if isinstance(json_response, dict):
                        for key, value in json_response.items():
                            if key == 'data' and isinstance(value, str) and len(value) > 200:
                                print(f"  {key}: {value[:200]}...")
                            else:
                                print(f"  {key}: {value}")
                    else:
                        print(json.dumps(json_response, indent=2, ensure_ascii=False))
                        
                except json.JSONDecodeError:
                    print("✗ 响应不是有效的JSON格式")
                    print(f"原始响应: {response.text[:500]}...")
            else:
                print(f"✗ 请求失败，状态码: {response.status_code}")
                print(f"错误响应: {response.text}")
                
        except requests.exceptions.Timeout:
            print("✗ 请求超时（60秒）")
        except requests.exceptions.ConnectionError:
            print("✗ 连接失败，请确认服务器是否运行")
        except Exception as e:
            print(f"✗ 请求异常: {str(e)}")
        
        print("-" * 60)
    
    print(f"\n测试总结: {success_count}/{total_count} 个测试用例成功")
    return success_count == total_count

def test_error_cases():
    """测试错误情况"""
    print_separator("错误情况测试")
    
    url = "http://127.0.0.1:7567/zte-ibo-acm-productretrieve/faq"
    
    error_cases = [
        {
            "name": "空输入测试",
            "headers": {
                'X-Emp-No': '0668001399',
                'Content-Type': 'application/json'
            },
            "data": {"text": ""},
            "expected": "应该返回空输入错误"
        },
        {
            "name": "缺少X-Emp-No头部",
            "headers": {'Content-Type': 'application/json'},
            "data": {"text": "测试问题"},
            "expected": "应该返回缺少员工号错误"
        },
        {
            "name": "缺少text字段",
            "headers": {
                'X-Emp-No': '0668001399',
                'Content-Type': 'application/json'
            },
            "data": {},
            "expected": "应该返回缺少文本字段错误"
        },
        {
            "name": "无效JSON",
            "headers": {
                'X-Emp-No': '0668001399',
                'Content-Type': 'application/json'
            },
            "data": "invalid json",
            "expected": "应该返回JSON解析错误"
        }
    ]
    
    for i, case in enumerate(error_cases, 1):
        print(f"\n{i}. {case['name']}")
        print(f"期望结果: {case['expected']}")
        
        try:
            if isinstance(case['data'], str):
                response = requests.post(url, headers=case['headers'], 
                                       data=case['data'], proxies=proxies, timeout=10)
            else:
                response = requests.post(url, headers=case['headers'], 
                                       json=case['data'], proxies=proxies, timeout=10)
            
            print(f"状态码: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    json_response = response.json()
                    print("响应内容:")
                    print(json.dumps(json_response, indent=2, ensure_ascii=False))
                except:
                    print(f"响应: {response.text}")
            else:
                print(f"错误响应: {response.text}")
                
        except Exception as e:
            print(f"异常: {str(e)}")

def check_server_status():
    """检查服务器状态"""
    print_separator("服务器状态检查")
    
    try:
        response = requests.get("http://127.0.0.1:7567/zte-ibo-acm-productretrieve/info", 
                              proxies=proxies, timeout=5)
        if response.status_code == 200:
            print("✓ 服务器正在运行")
            print(f"响应: {response.text}")
            return True
        else:
            print(f"✗ 服务器响应异常，状态码: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("✗ 无法连接到服务器")
        print("请确认:")
        print("1. 服务器是否已启动")
        print("2. 端口7567是否正确")
        print("3. 是否已重启服务器以使配置生效")
        return False
    except Exception as e:
        print(f"✗ 服务器检查异常: {str(e)}")
        return False

def main():
    """主测试函数"""
    print(f"开始FAQ接口测试（无token验证） - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("注意: 此测试需要先重启服务器以使配置生效")
    print_separator()
    
    # 检查服务器状态
    if not check_server_status():
        return
    
    # 执行测试
    success = test_faq_api()
    test_error_cases()
    
    print_separator("测试完成")
    print(f"测试结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    if success:
        print("✓ 所有基础测试用例通过")
    else:
        print("✗ 部分测试用例失败")

if __name__ == "__main__":
    main()
