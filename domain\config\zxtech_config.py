import sys
from utils.encryption_util import decrypt
from utils.load_config import load_config_from_net, load_config
from config import config as local_config

def deal_config(config):
    key_flag=config['key-flag']
    llm_secret_en=config['LLM']['llm_headers']['Authorization']
    es_secret_en=config['Knowledge']['access_secret']
    es_access_key=config['Knowledge']['access_key']
    config['LLM']['llm_headers']['Authorization']=decrypt(llm_secret_en,key_flag)
    config['Knowledge']['access_secret']=decrypt(es_secret_en,key_flag)
    config['Knowledge']['access_key'] = decrypt(es_access_key, key_flag)
    return config

# 使用本地配置而不是远程配置
config = deal_config(local_config)


