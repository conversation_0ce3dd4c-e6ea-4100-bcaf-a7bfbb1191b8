import copy
import json
from flask import Blueprint, request, g
from service.zxtech_service import _ZXTECH_
from domain.constants.Enums import Hints
## 在这设置配置
from domain.config.zxtech_config import config
from utils.logger.logger_util import logger
from utils.utils import wrapper_response
zxtech_subscribe = Blueprint('zxtech_subscribe', __name__, url_prefix='/zte-ibo-acm-productretrieve')


# 产品技术问答接口
@zxtech_subscribe.route('/faq', methods=['POST'], strict_slashes=False)
def product_chat():
    # 入参处理
    data = json.loads(request.get_data(as_text=True))
    # 用户输入的文本
    XEmpNo = request.headers.get("X-Emp-No")
    if not XEmpNo:
        return Hints.EMP_ERROR.value
    query = data.get("text")
    history = data.get('history')
    rewriteText = data.get('rewriteText')
    g.XEmpNo = XEmpNo
    g.query = query
    g_copy = copy.deepcopy(g.__dict__)
    if not query:
        logger.warn('input empty \n')
        return wrapper_response('0004', "Empty Input")
    logger.info('''
    ---------- START OF LOG ----------
    ''')
    logger.info('[用户工号]:' + XEmpNo + ' ' + '[用户问题]:' + query)
    chat_uuid = data.get("chatUuid", '')
    _zxtech_ = _ZXTECH_(config)
    return _zxtech_(query,XEmpNo,history,rewriteText,g=g_copy)

@zxtech_subscribe.route('/info', methods=['POST', 'GET'])
def status_query():
    """
    get请求，返回接口状态
    :return: {"status": "ok"}
    """
    if request.method == 'GET' or request.method == 'POST':
        result = {"status": "ok"}
        return json.dumps(result)