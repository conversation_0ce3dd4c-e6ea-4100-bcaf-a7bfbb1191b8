# ZTE产品智能问答系统 - 项目架构分析报告

## 📋 项目概述

**项目名称**: zte-ibo-acm-productretrieve  
**项目类型**: 基于Flask的企业级智能问答系统  
**技术架构**: AgenticRAG（代理检索增强生成）  
**主要功能**: 中兴通讯产品技术问答、多轮对话、多语言支持  

## 🏗️ 系统架构

### 整体架构设计

系统采用分层架构设计，从上到下包括：

1. **用户接口层**: Flask REST API
2. **控制层**: 请求路由和身份验证
3. **服务层**: 核心业务逻辑处理
4. **检索层**: 多源数据检索
5. **AI处理层**: 向量化、重排序、生成
6. **数据层**: 多种数据存储

### 核心组件

#### 1. Web服务层
- **Flask 2.3.3**: 主要Web框架
- **Flask-CORS**: 跨域资源共享支持
- **Werkzeug**: WSGI工具包

#### 2. 业务逻辑层
- **Controller**: `controller/zxtech_controller.py` - API端点定义
- **Service**: `service/zxtech_service.py` - 核心业务逻辑
- **Domain**: 领域模型和配置管理

#### 3. AI处理层
- **向量化**: BGE-M3模型进行文本向量化
- **检索**: 多源检索（ES + Milvus + KG）
- **重排序**: BGE-M3 Reranker结果优化
- **生成**: 大语言模型答案生成

#### 4. 数据存储层
- **Elasticsearch**: 文本检索和全文搜索
- **Milvus**: 向量数据库，语义相似性检索
- **知识图谱**: 结构化产品知识存储

## 🔄 业务流程

### 主要处理流程

1. **请求接收**: 用户通过POST请求发送问题
2. **身份验证**: 验证X-Emp-No和X-Auth-Value
3. **预处理**: 
   - 语言检测（中文/英文）
   - 查询重写（多轮对话处理）
   - 实体提取（产品型号识别）
4. **检索策略**:
   - 有实体：产品/系列/版本特定检索
   - 无实体：全局知识检索
5. **多源检索**: 并行执行ES、Milvus、KG检索
6. **结果处理**: 重排序、去重、格式化
7. **答案生成**: LLM基于检索结果生成答案
8. **响应返回**: 格式化响应并记录日志

### 检索策略

系统根据实体提取结果采用不同的检索策略：

- **无实体**: 全局检索所有知识库
- **有产品型号**: 检索特定产品相关文档
- **有系列号**: 检索系列相关文档
- **有版本号**: 检索特定版本文档

## 📁 目录结构

```
zte-ibo-acm-productretrieve/
├── main.py                 # Flask应用入口
├── config.py              # 配置管理
├── requirements.txt       # 依赖包列表
├── Dockerfile            # Docker构建文件
├── controller/           # 控制器层
│   └── zxtech_controller.py
├── service/              # 服务层
│   └── zxtech_service.py
├── domain/               # 领域层
│   ├── config/          # 领域配置
│   ├── constants/       # 常量定义
│   └── entity/          # 实体定义
├── embedding/            # 向量化模块
│   └── get_embedding.py
├── retrieve/             # 检索模块
│   ├── Recall_data.py   # ES检索
│   ├── milvus_recall.py # 向量检索
│   └── kg_retrieve/     # 知识图谱检索
├── rerank/               # 重排序模块
│   └── bge_m3_reranker_v2.py
├── llm/                  # 大模型模块
│   └── LLMResult.py
├── utils/                # 工具模块
│   ├── logger/          # 日志系统
│   ├── tokenVerify.py   # 身份验证
│   └── ...              # 其他工具
├── prompt/               # 提示词模块
│   ├── PromptLoader.py
│   └── prompt_instance/
└── resource/             # 资源文件
    └── config/
```

## 🛠️ 技术栈

### 核心依赖

| 组件 | 版本 | 用途 |
|------|------|------|
| Flask | 2.3.3 | Web框架 |
| PyMilvus | 2.4.1 | 向量数据库客户端 |
| NumPy | 1.26.4 | 数值计算 |
| Pandas | 2.0.3 | 数据处理 |
| Scikit-learn | 1.5.0 | 机器学习工具 |
| Requests | 2.32.3 | HTTP客户端 |
| PyYAML | 6.0.1 | 配置文件解析 |
| PyCryptodome | 3.20.0 | 加密解密 |

### AI/ML组件

- **BGE-M3**: 多语言向量化模型
- **BGE-M3 Reranker**: 结果重排序模型
- **Spacy-pkuseg**: 中文分词工具
- **大语言模型**: 通过API调用的生成模型

## 🔐 安全特性

### 身份验证
- **Token验证**: 基于X-Auth-Value的令牌验证
- **用户识别**: X-Emp-No员工工号验证
- **路径控制**: 可配置的验证排除和拦截路径

### 数据安全
- **加密存储**: 敏感配置信息加密存储
- **安全传输**: HTTPS通信和证书验证
- **日志脱敏**: 敏感信息过滤和脱敏

## 📊 监控和日志

### 日志系统
- **结构化日志**: 使用colorlog进行彩色日志输出
- **请求追踪**: 完整的请求/响应日志记录
- **性能监控**: 时间记录和性能分析
- **错误追踪**: 详细的异常信息记录

### 链路追踪
- **Apache SkyWalking**: 分布式链路追踪
- **gRPC支持**: 微服务间通信监控
- **Kafka集成**: 消息队列监控

## 🚀 部署和运维

### 本地开发
```bash
# 安装依赖
pip install -r requirements.txt --index-url https://artnj.zte.com.cn/artifactory/api/pypi/public-pypi-virtual/simple --trusted-host artnj.zte.com.cn

# 运行应用
python main.py
```

### Docker部署
```bash
# 构建镜像
docker build -t zte-ibo-acm-productretrieve .

# 运行容器
docker run -p 5001:5001 zte-ibo-acm-productretrieve
```

### 配置管理
- **Apollo配置中心**: 生产环境配置管理
- **本地YAML**: 开发环境配置文件
- **环境隔离**: dev/test/prod环境配置分离

## 📈 性能优化

### 检索优化
- **并行检索**: ES、Milvus、KG并行执行
- **结果缓存**: 向量化结果缓存
- **连接池**: 数据库连接池管理

### 内存优化
- **结果去重**: 避免重复内容占用内存
- **分页处理**: 大结果集分页处理
- **垃圾回收**: 及时释放不需要的对象

## 🔮 扩展性设计

### 模块化设计
- **插件化架构**: 各组件独立可替换
- **配置驱动**: 通过配置控制功能开关
- **接口标准化**: 统一的接口规范

### 水平扩展
- **无状态设计**: 服务无状态，支持负载均衡
- **数据库分片**: 支持数据库水平分片
- **缓存层**: Redis缓存支持

---

**报告生成时间**: 2025-08-01  
**分析工具**: Augment Agent  
**版本**: v1.0
