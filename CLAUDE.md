# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 项目架构

这是一个基于Flask的中兴通讯产品智能问答系统，采用AgenticRAG架构处理产品技术查询。系统整合了多种检索源（Elasticsearch、Milvus向量数据库、知识图谱），结合重排序和大语言模型生成。

### 核心组件

- **控制器层**: `controller/zxtech_controller.py` - Flask API端点，主要入口为 `/zte-ibo-acm-productretrieve/faq`
- **服务层**: `service/zxtech_service.py` - `_ZXTECH_` 类中的主要业务逻辑
- **检索系统**: 
  - `retrieve/milvus_recall.py` - 使用Milvus进行向量相似性搜索
  - `retrieve/Recall_data.py` - 基于Elasticsearch的检索
  - `retrieve/kg_retrieve/` - 知识图谱检索
- **重排序**: `rerank/bge_m3_reranker_v2.py` - BGE-M3模型结果重排序
- **大模型集成**: `llm/LLMResult.py` - 大语言模型API集成
- **向量化**: `embedding/get_embedding.py` - 文本向量化生成
- **查询重写**: `rewrite/rewrite_model.py` - 查询预处理和重写

### 关键处理流程

1. 查询预处理和语言检测 (`utils/judge_string_type.py`)
2. 从用户查询中提取实体
3. 多源检索 (ES + Milvus + KG)
4. 使用BGE-M3进行结果重排序
5. 基于检索上下文的大模型生成
6. 响应格式化和日志记录

### 配置管理

- **主配置**: `config.py` - 使用Apollo配置服务或本地YAML文件
- **领域配置**: `domain/config/zxtech_config.py` - 应用特定配置
- **常量**: `domain/constants/` - 枚举、提示词和系统常量

## 开发命令

### 运行应用
```bash
python main.py
```
应用在配置中指定的主机/端口上运行（默认开发设置）。

### Docker部署
```bash
docker build -t zte-ibo-acm-productretrieve .
docker run -p 5001:5001 zte-ibo-acm-productretrieve
```

### 安装依赖
```bash
pip install -r requirements.txt --index-url https://artnj.zte.com.cn/artifactory/api/pypi/public-pypi-virtual/simple --trusted-host artnj.zte.com.cn
```

## 重要开发注意事项

### 身份验证和请求头
- 所有API请求需要 `X-Emp-No`（员工工号）和 `X-Auth-Value`（令牌）请求头
- 令牌验证在 `main.py` 的 before_request 钩子中处理
- 使用 `utils/tokenVerify.py` 处理身份验证逻辑

### 日志系统
- `utils/logger/` 中的完整日志系统
- 带用户跟踪的请求/响应日志
- 带时间记录的性能监控

### 实体处理
- 产品实体提取是系统核心
- 实体映射在 `utils/entity_util.py` 中处理
- 实体结果影响检索策略

### 向量操作
- Milvus向量数据库集成用于语义搜索
- BGE-M3嵌入用于查询和文档表示
- 余弦相似度计算在 `utils/cos_sim.py` 中

### 提示词管理
- `prompt/prompt_instance/` 中的丰富提示词模板
- 不同产品类别和语言的不同提示词
- 通过 `prompt/PromptLoader.py` 加载提示词

## 测试和质量

### API测试
- 使用 `/info` 端点检查服务健康状态
- `ui/` 目录中的测试文件用于手动测试
- `test.py` 中的主要测试逻辑

### 配置
- 环境特定配置应通过Apollo或本地YAML管理
- 开发环境在 `config.py` 中使用 `env = 'dev'`
- `service_setting.cfg` 中的服务设置

## 安全考虑

- 所有端点基于令牌的身份验证
- 请求日志包含敏感请求头过滤
- `utils/encryption_util.py` 中提供加密工具
- 不应将机密信息提交到仓库