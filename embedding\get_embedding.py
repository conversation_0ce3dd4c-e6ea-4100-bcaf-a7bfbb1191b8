import requests
from utils.logger.logger_util import logger


class GetEmbedding:

    def __init__(self, config):
        self.embedding_url = config['Embedding']['url']
    def get_embedding(self, question):
        bo = {
            "text": question
        }
        try:
            receive = requests.post(
                json=bo,
                url=self.embedding_url,
                verify=True,
                timeout=30
            )
            result = receive.json()['bo']['sentence_embeddings']
            return result
        except:
            logger.error(f"embedding接口报错")
            return None

    def post_url_m3(self,query):
        query=self.deal_query(query,8000)
        if isinstance(query, str):
            query = [query]
        receive = requests.post(
            json = {"text":query},
            url = self.embedding_url,
            verify = True
        )
        try:
            result = receive.json()['bo']['sentence_embeddings']
            return result
        except Exception as e:
            logger.error(f"embedding响应报错：{receive}")
            logger.error("embedding超时")
            return None

    # Started by AICoder, pid:se0fckf6d0m1a4f14ad20a747033b81e51365acf
    def deal_query(self, query, length_broad: int = 8000):
        """
        处理查询字符串或列表中的字符串，确保每个字符串的长度不超过指定的最大长度。

        :param query: 查询字符串或包含字符串的列表
        :param length_broad: 最大长度，默认为8000
        :return: 处理后的查询结果
        """
        if isinstance(query, str):
            return query[:length_broad]
        elif isinstance(query, list):
            # 使用列表推导式处理列表中的每个元素
            return [s[:length_broad] if isinstance(s, str) else s for s in query]
        else:
            # 如果query既不是字符串也不是列表，则直接返回原值
            return query

    # Ended by AICoder, pid:se0fckf6d0m1a4f14ad20a747033b81e51365acf
