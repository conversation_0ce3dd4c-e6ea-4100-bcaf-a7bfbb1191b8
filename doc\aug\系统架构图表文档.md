# ZTE产品智能问答系统 - 系统架构图表文档

## 📊 架构图表集合

本文档包含了ZTE产品智能问答系统的各种架构图表，帮助理解系统的整体设计和技术实现。

---

## 1. 整体系统架构图

```mermaid
graph TB
    subgraph "用户层"
        U[用户请求] --> API[Flask API<br>/zte-ibo-acm-productretrieve/faq]
    end
    
    subgraph "控制层"
        API --> C[Controller<br>zxtech_controller.py]
        C --> AUTH[身份验证<br>Token验证]
        AUTH --> S[Service Layer<br>_ZXTECH_类]
    end
    
    subgraph "核心处理层"
        S --> PRE[预处理模块]
        PRE --> LANG[语言检测]
        PRE --> REWRITE[查询重写]
        PRE --> ENTITY[实体提取]
        
        ENTITY --> EMBED[向量化<br>BGE-M3]
        EMBED --> RETRIEVE[多源检索]
    end
    
    subgraph "检索层"
        RETRIEVE --> ES[Elasticsearch<br>文本检索]
        RETRIEVE --> MILVUS[Milvus<br>向量检索]
        RETRIEVE --> KG[知识图谱<br>KG检索]
    end
    
    subgraph "后处理层"
        ES --> RERANK[重排序<br>BGE-M3 Reranker]
        MILVUS --> RERANK
        KG --> RERANK
        RERANK --> LLM[大语言模型<br>生成答案]
    end
    
    subgraph "响应层"
        LLM --> RESP[响应格式化]
        RESP --> LOG[日志记录]
        LOG --> U
    end
    
    style U fill:#e1f5fe
    style API fill:#f3e5f5
    style S fill:#fff3e0
    style RETRIEVE fill:#e8f5e8
    style LLM fill:#fce4ec
```

---

## 2. 项目目录结构图

```mermaid
graph TD
    ROOT[zte-ibo-acm-productretrieve] --> MAIN[main.py<br>Flask应用入口]
    ROOT --> CONFIG[config.py<br>配置管理]
    
    ROOT --> CONTROLLER[controller/<br>控制器层]
    CONTROLLER --> CTRL_FILE[zxtech_controller.py<br>API端点定义]
    
    ROOT --> SERVICE[service/<br>服务层]
    SERVICE --> SVC_FILE[zxtech_service.py<br>核心业务逻辑]
    
    ROOT --> DOMAIN[domain/<br>领域层]
    DOMAIN --> DOM_CONFIG[config/<br>领域配置]
    DOMAIN --> DOM_CONST[constants/<br>常量定义]
    DOMAIN --> DOM_ENTITY[entity/<br>实体定义]
    
    ROOT --> EMBEDDING[embedding/<br>向量化模块]
    EMBEDDING --> EMB_FILE[get_embedding.py<br>BGE-M3向量化]
    
    ROOT --> RETRIEVE[retrieve/<br>检索模块]
    RETRIEVE --> RET_ES[Recall_data.py<br>ES检索]
    RETRIEVE --> RET_MILVUS[milvus_recall.py<br>向量检索]
    RETRIEVE --> RET_KG[kg_retrieve/<br>知识图谱检索]
    
    ROOT --> RERANK[rerank/<br>重排序模块]
    RERANK --> RRK_FILE[bge_m3_reranker_v2.py<br>BGE-M3重排序]
    
    ROOT --> LLM[llm/<br>大模型模块]
    LLM --> LLM_FILE[LLMResult.py<br>大模型调用]
    
    ROOT --> UTILS[utils/<br>工具模块]
    UTILS --> UTIL_LOG[logger/<br>日志系统]
    UTILS --> UTIL_AUTH[tokenVerify.py<br>身份验证]
    UTILS --> UTIL_OTHER[其他工具...]
    
    ROOT --> PROMPT[prompt/<br>提示词模块]
    PROMPT --> PROMPT_LOADER[PromptLoader.py<br>提示词加载器]
    PROMPT --> PROMPT_INST[prompt_instance/<br>提示词模板]
    
    style ROOT fill:#e3f2fd
    style MAIN fill:#fff3e0
    style SERVICE fill:#e8f5e8
    style RETRIEVE fill:#fce4ec
    style LLM fill:#f3e5f5
```

---

## 3. 核心业务处理流程图

```mermaid
flowchart TD
    START([用户提问]) --> INPUT[接收用户输入]
    INPUT --> VALIDATE{身份验证}
    VALIDATE -->|失败| ERROR[返回验证失败]
    VALIDATE -->|成功| PREPROCESS[预处理]
    
    PREPROCESS --> LANG_DETECT[语言检测<br>中文/英文]
    PREPROCESS --> QUERY_REWRITE[查询重写<br>多轮对话处理]
    PREPROCESS --> ENTITY_EXTRACT[实体提取<br>产品型号识别]
    
    ENTITY_EXTRACT --> HAS_ENTITY{是否提取到实体?}
    
    HAS_ENTITY -->|否| NO_ENTITY[无实体处理流程]
    NO_ENTITY --> GLOBAL_SEARCH[全局检索]
    
    HAS_ENTITY -->|是| CHECK_PRODUCT{产品型号匹配?}
    CHECK_PRODUCT -->|否| SERIES_SEARCH[系列号检索]
    CHECK_PRODUCT -->|是| VERSION_CHECK{是否指定版本?}
    
    VERSION_CHECK -->|是| VERSION_SEARCH[版本文档检索]
    VERSION_CHECK -->|否| PRODUCT_SEARCH[产品文档检索]
    
    GLOBAL_SEARCH --> MULTI_RETRIEVE[多源检索]
    SERIES_SEARCH --> MULTI_RETRIEVE
    VERSION_SEARCH --> MULTI_RETRIEVE
    PRODUCT_SEARCH --> MULTI_RETRIEVE
    
    MULTI_RETRIEVE --> ES_SEARCH[Elasticsearch<br>文本检索]
    MULTI_RETRIEVE --> VECTOR_SEARCH[Milvus<br>向量检索]
    MULTI_RETRIEVE --> KG_SEARCH[知识图谱<br>结构化检索]
    
    ES_SEARCH --> RERANK[BGE-M3重排序]
    VECTOR_SEARCH --> RERANK
    KG_SEARCH --> RERANK
    
    RERANK --> DEDUP[结果去重]
    DEDUP --> LLM_GENERATE[大模型生成答案]
    LLM_GENERATE --> FORMAT[响应格式化]
    FORMAT --> LOG[记录日志]
    LOG --> RESPONSE([返回答案])
    
    style START fill:#e1f5fe
    style VALIDATE fill:#fff3e0
    style HAS_ENTITY fill:#e8f5e8
    style MULTI_RETRIEVE fill:#fce4ec
    style LLM_GENERATE fill:#f3e5f5
    style RESPONSE fill:#e1f5fe
```

---

## 4. 技术栈和依赖关系图

```mermaid
graph TB
    subgraph "Web框架层"
        FLASK[Flask 2.3.3<br>Web框架]
        CORS[Flask-Cors 4.0.2<br>跨域支持]
        WERKZEUG[Werkzeug 3.0.6<br>WSGI工具]
    end
    
    subgraph "数据处理层"
        NUMPY[NumPy 1.26.4<br>数值计算]
        PANDAS[Pandas 2.0.3<br>数据处理]
        SKLEARN[Scikit-learn 1.5.0<br>机器学习]
    end
    
    subgraph "向量数据库层"
        MILVUS[PyMilvus 2.4.1<br>向量数据库]
        EMBEDDING[BGE-M3<br>向量化模型]
    end
    
    subgraph "搜索引擎层"
        ES[Elasticsearch<br>文本检索]
        KG[知识图谱<br>结构化数据]
    end
    
    subgraph "NLP处理层"
        SPACY[Spacy-pkuseg 0.0.33<br>中文分词]
        RERANK[BGE-M3 Reranker<br>结果重排序]
    end
    
    subgraph "安全和配置层"
        CRYPTO[PyCryptodome 3.20.0<br>加密解密]
        YAML[PyYAML 6.0.1<br>配置文件]
        APOLLO[Apollo<br>配置中心]
    end
    
    subgraph "通信层"
        REQUESTS[Requests 2.32.3<br>HTTP客户端]
        GRPC[gRPC 1.59.5<br>RPC通信]
        KAFKA[Kafka-python-ng 2.2.2<br>消息队列]
    end
    
    subgraph "监控层"
        SKYWALKING[Apache-SkyWalking 1.1.0<br>链路追踪]
        LOGGING[日志系统<br>结构化日志]
    end
    
    FLASK --> NUMPY
    FLASK --> PANDAS
    MILVUS --> EMBEDDING
    ES --> SPACY
    CRYPTO --> YAML
    REQUESTS --> GRPC
    
    style FLASK fill:#e3f2fd
    style MILVUS fill:#e8f5e8
    style ES fill:#fff3e0
    style CRYPTO fill:#fce4ec
```

---

## 5. 数据流向时序图

```mermaid
sequenceDiagram
    participant User as 用户
    participant API as Flask API
    participant Auth as 身份验证
    participant Service as 业务服务
    participant Entity as 实体提取
    participant Embed as 向量化
    participant ES as Elasticsearch
    participant Milvus as Milvus向量库
    participant KG as 知识图谱
    participant Rerank as 重排序
    participant LLM as 大语言模型
    
    User->>API: POST /faq {text, history}
    API->>Auth: 验证X-Emp-No和Token
    Auth-->>API: 验证结果
    
    API->>Service: 调用_ZXTECH_服务
    Service->>Service: 查询重写和预处理
    Service->>Entity: 提取产品实体
    Entity-->>Service: 返回实体信息
    
    Service->>Embed: 生成查询向量
    Embed-->>Service: 返回向量表示
    
    par 并行检索
        Service->>ES: 文本检索
        ES-->>Service: ES检索结果
    and
        Service->>Milvus: 向量相似性检索
        Milvus-->>Service: 向量检索结果
    and
        Service->>KG: 知识图谱检索
        KG-->>Service: KG检索结果
    end
    
    Service->>Rerank: BGE-M3重排序
    Rerank-->>Service: 重排序结果
    
    Service->>LLM: 生成最终答案
    LLM-->>Service: 生成的答案
    
    Service-->>API: 格式化响应
    API-->>User: 返回答案
```

---

## 📝 图表说明

### 架构设计原则
1. **分层解耦**: 各层职责明确，降低耦合度
2. **模块化**: 功能模块独立，便于维护和扩展
3. **可扩展性**: 支持水平扩展和功能扩展
4. **高可用**: 多源检索保证服务可用性

### 关键技术特点
1. **AgenticRAG架构**: 智能代理检索增强生成
2. **多源融合**: ES + Milvus + KG三路检索
3. **智能重排序**: BGE-M3模型优化结果排序
4. **企业级安全**: 完整的身份验证和数据加密

### 性能优化策略
1. **并行处理**: 多源检索并行执行
2. **结果缓存**: 向量化结果缓存机制
3. **连接池**: 数据库连接池管理
4. **异步处理**: 非阻塞I/O操作

---

**文档版本**: v1.0  
**更新时间**: 2025-08-01  
**维护团队**: ZTE AI团队
