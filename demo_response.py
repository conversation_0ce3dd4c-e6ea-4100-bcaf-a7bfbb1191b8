#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
创建一个简单的演示响应，用于当外部API不可用时提供基本功能
"""

import json
from flask import Flask, request
from flask_cors import CORS
from utils.utils import wrapper_response
from utils.logger.logger_util import logger

app = Flask(__name__)

@app.before_request  
def before_request():
    pass

@app.route('/zte-ibo-acm-productretrieve/info', methods=['POST', 'GET'])
def status_query():
    """健康检查端点"""
    if request.method == 'GET' or request.method == 'POST':
        result = {"status": "ok"}
        return json.dumps(result)

@app.route('/zte-ibo-acm-productretrieve/faq', methods=['POST'])
def demo_faq():
    """演示FAQ端点，返回预设的模拟响应"""
    try:
        data = json.loads(request.get_data(as_text=True))
        XEmpNo = request.headers.get("X-Emp-No")
        query = data.get("text", "").strip()
        
        if not XEmpNo:
            return wrapper_response('0002', 'Missing X-Emp-No header')
        
        if not query:
            return wrapper_response('0004', "Empty Input")
        
        logger.info(f'[用户工号]:{XEmpNo} [用户问题]:{query}')
        
        # 根据问题内容返回不同的模拟响应
        if "M6000" in query or "接口" in query:
            mock_response = {
                "code": "0000",
                "msg": "success", 
                "data": {
                    "result": "根据产品文档，M6000-8S系列交换机支持以下主要接口类型：\n\n1. **管理接口**\n   - Console接口：用于本地管理\n   - USB接口：用于配置文件导入导出\n\n2. **业务接口**\n   - 10GE SFP+接口：支持光纤和电缆连接\n   - 40GE QSFP+接口：高速数据传输\n   - 100GE接口：超高速网络连接\n\n3. **扩展接口**\n   - 支持多种板卡扩展\n   - 可根据需求配置不同接口模块\n\n**注意**：这是基于本地配置的模拟响应，实际接口配置请参考最新产品手册。",
                    "source": "本地演示模式",
                    "references": []
                }
            }
        else:
            mock_response = {
                "code": "0000", 
                "msg": "success",
                "data": {
                    "result": f"您咨询的问题是：{query}\n\n目前系统运行在演示模式下，无法连接到完整的知识库和AI服务。\n\n在正常生产环境中，系统会：\n1. 提取问题中的产品实体\n2. 从多个知识源检索相关信息\n3. 使用AI模型生成准确答案\n\n如需获取准确的技术信息，请确保网络连接正常或联系系统管理员。",
                    "source": "本地演示模式", 
                    "references": []
                }
            }
        
        return json.dumps(mock_response, ensure_ascii=False)
        
    except Exception as e:
        logger.error(f"演示模式处理出错: {str(e)}")
        return wrapper_response('0001', f'演示模式处理出错: {str(e)}')

if __name__ == '__main__':
    CORS(app, supports_credentials=True)
    print("=== 演示模式服务器启动 ===")
    print("本服务运行在演示模式，提供基本的模拟响应")
    print("访问地址: http://0.0.0.0:7567")
    print("健康检查: GET /zte-ibo-acm-productretrieve/info") 
    print("问答接口: POST /zte-ibo-acm-productretrieve/faq")
    print("==============================")
    app.run(host='0.0.0.0', port=7567, debug=False)