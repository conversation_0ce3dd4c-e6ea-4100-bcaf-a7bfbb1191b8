# ZTE产品智能问答系统 - API接口文档

## 📋 接口概述

本文档描述了ZTE产品智能问答系统的REST API接口规范，包括请求格式、响应格式、错误处理等详细信息。

---

## 🌐 基础信息

- **Base URL**: `http://host:port/zte-ibo-acm-productretrieve`
- **协议**: HTTP/HTTPS
- **数据格式**: JSON
- **字符编码**: UTF-8

---

## 🔐 身份验证

所有API请求都需要在HTTP Header中包含以下认证信息：

| Header名称 | 类型 | 必填 | 说明 |
|-----------|------|------|------|
| X-Emp-No | String | 是 | 员工工号 |
| X-Auth-Value | String | 是 | 认证令牌 |
| Content-Type | String | 是 | application/json |

### 认证示例

```http
POST /zte-ibo-acm-productretrieve/faq HTTP/1.1
Host: your-domain.com
Content-Type: application/json
X-Emp-No: 12345678
X-Auth-Value: your-auth-token

{
  "text": "ZXR10 M6000-8S Plus的配置方法是什么？"
}
```

---

## 📡 API接口

### 1. 产品技术问答接口

#### 接口信息
- **URL**: `/faq`
- **方法**: POST
- **功能**: 处理产品技术相关问题，支持多轮对话

#### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| text | String | 是 | 用户问题文本 |
| history | Array | 否 | 历史对话记录 |
| rewriteText | Object | 否 | 查询重写文本 |
| chatUuid | String | 否 | 会话唯一标识 |

#### 请求示例

```json
{
  "text": "ZXR10 M6000-8S Plus的端口配置方法？",
  "history": [
    {
      "role": "user",
      "content": "什么是ZXR10 M6000系列？"
    },
    {
      "role": "assistant", 
      "content": "ZXR10 M6000系列是中兴通讯的高端路由器产品..."
    }
  ],
  "rewriteText": null,
  "chatUuid": "uuid-12345-67890"
}
```

#### 响应格式

**成功响应**:
```json
{
  "code": "0000",
  "message": "success",
  "data": {
    "answer": "ZXR10 M6000-8S Plus端口配置方法如下：\n1. 登录设备管理界面...",
    "sources": [
      {
        "documentName": "ZXR10 M6000-8S Plus配置指南",
        "documentId": "doc_12345",
        "relevance": 0.95
      }
    ],
    "chatUuid": "uuid-12345-67890",
    "timestamp": "2025-08-01T10:30:00Z"
  }
}
```

**错误响应**:
```json
{
  "code": "0002",
  "message": "UAC Verify Fail",
  "data": null
}
```

#### 响应字段说明

| 字段名 | 类型 | 说明 |
|--------|------|------|
| code | String | 响应状态码 |
| message | String | 响应消息 |
| data.answer | String | 生成的答案内容 |
| data.sources | Array | 参考文档来源 |
| data.chatUuid | String | 会话标识 |
| data.timestamp | String | 响应时间戳 |

---

### 2. 服务状态查询接口

#### 接口信息
- **URL**: `/info`
- **方法**: GET/POST
- **功能**: 查询服务运行状态

#### 请求参数
无需参数

#### 请求示例

```http
GET /zte-ibo-acm-productretrieve/info HTTP/1.1
Host: your-domain.com
```

#### 响应示例

```json
{
  "status": "ok"
}
```

---

## 📊 状态码说明

| 状态码 | 说明 | 处理建议 |
|--------|------|----------|
| 0000 | 成功 | 正常处理 |
| 0002 | 身份验证失败 | 检查认证信息 |
| 0004 | 输入为空 | 检查请求参数 |
| 0005 | 服务超时 | 重试请求 |
| 0006 | 系统错误 | 联系技术支持 |

---

## 🔄 业务场景示例

### 场景1: 单轮问答

```json
// 请求
{
  "text": "ZXR10 M6000-2S4 PLUS支持哪些接口类型？"
}

// 响应
{
  "code": "0000",
  "message": "success", 
  "data": {
    "answer": "ZXR10 M6000-2S4 PLUS支持以下接口类型：\n1. 千兆以太网接口\n2. 万兆以太网接口\n3. 串行接口\n4. 管理接口",
    "sources": [
      {
        "documentName": "ZXR10 M6000-2S4 PLUS产品规格书",
        "documentId": "spec_001",
        "relevance": 0.98
      }
    ]
  }
}
```

### 场景2: 多轮对话

```json
// 第一轮请求
{
  "text": "ZXR10 M6000系列有哪些型号？",
  "chatUuid": "chat_001"
}

// 第二轮请求（基于历史对话）
{
  "text": "M6000-8S Plus的价格是多少？",
  "history": [
    {
      "role": "user",
      "content": "ZXR10 M6000系列有哪些型号？"
    },
    {
      "role": "assistant",
      "content": "ZXR10 M6000系列包括：M6000-2S4 PLUS、M6000-8S Plus等型号..."
    }
  ],
  "chatUuid": "chat_001"
}
```

### 场景3: 版本特定查询

```json
// 请求
{
  "text": "ZXR10 M6000-8S Plus V6.0版本的新特性有哪些？"
}

// 系统会自动识别版本号V6.0，检索对应版本文档
```

---

## ⚠️ 错误处理

### 常见错误及解决方案

1. **认证失败 (0002)**
   - 检查X-Emp-No和X-Auth-Value是否正确
   - 确认令牌是否过期

2. **输入为空 (0004)**
   - 确保text字段不为空
   - 检查JSON格式是否正确

3. **服务超时 (0005)**
   - 重试请求
   - 检查网络连接
   - 简化查询内容

4. **系统错误 (0006)**
   - 查看系统日志
   - 联系技术支持团队

---

## 🚀 性能优化建议

### 请求优化
1. **合理使用历史对话**: 避免传递过长的历史记录
2. **精确描述问题**: 提供具体的产品型号和版本信息
3. **避免重复请求**: 使用chatUuid管理会话状态

### 响应处理
1. **异步处理**: 对于复杂查询，考虑异步处理
2. **结果缓存**: 缓存常见问题的答案
3. **分页处理**: 对于大量参考文档，考虑分页返回

---

## 📝 开发注意事项

### 请求限制
- **请求频率**: 建议每秒不超过10次请求
- **文本长度**: 单次查询文本建议不超过1000字符
- **历史记录**: 历史对话建议不超过10轮

### 最佳实践
1. **错误重试**: 实现指数退避重试机制
2. **超时设置**: 设置合理的请求超时时间（建议60秒）
3. **日志记录**: 记录请求和响应日志便于调试
4. **监控告警**: 监控API调用成功率和响应时间

---

## 📞 技术支持

如有技术问题，请联系：
- **技术支持邮箱**: <EMAIL>
- **文档维护**: ZTE AI团队
- **更新频率**: 每月更新

---

**文档版本**: v1.0  
**最后更新**: 2025-08-01  
**API版本**: v1.0
