2025-07-29 16:49:52,231 ｜ INFO ｜ kafka.py ｜ close ｜ 486 ｜ Closing the Kafka producer with 0 secs timeout.
2025-07-29 16:49:52,231 ｜ INFO ｜ kafka.py ｜ close ｜ 502 ｜ Proceeding to force close the producer since pending requests could not be completed within timeout 0.
2025-07-29 16:49:52,232 ｜ INFO ｜ conn.py ｜ close ｜ 673 ｜ <BrokerConnection node_id=0 host=************:9092 <connected> [IPv4 ('************', 9092)]>: Closing connection. 
2025-07-29 16:50:09,783 ｜ INFO ｜ _internal.py ｜ _log ｜ 97 ｜ [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:10023
 * Running on http://*************:10023
2025-07-29 16:50:09,783 ｜ INFO ｜ _internal.py ｜ _log ｜ 97 ｜ [33mPress CTRL+C to quit[0m
2025-07-29 16:50:17,792 ｜ INFO ｜ _internal.py ｜ _log ｜ 97 ｜ 127.0.0.1 - - [29/Jul/2025 16:50:17] "POST /zte-ibo-acm-productretrieve/faq HTTP/1.1" 200 -
2025-07-29 16:51:10,760 ｜ ERROR ｜ milvus_helpers.py ｜ __init__ ｜ 17 ｜ Failed to connect Milvus: <MilvusException: (code=2, message=Fail connecting to server on **************:19530, illegal connection params or server unavailable)>
2025-07-29 16:51:10,761 ｜ ERROR ｜ app.py ｜ log_exception ｜ 1414 ｜ Exception on /zte-ibo-acm-productretrieve/faq [POST]
Traceback (most recent call last):
  File "/home/<USER>/anaconda3/envs/env_name/lib/python3.9/site-packages/pymilvus/client/grpc_handler.py", line 144, in _wait_for_channel_ready
    grpc.channel_ready_future(self._channel).result(timeout=timeout)
  File "/home/<USER>/anaconda3/envs/env_name/lib/python3.9/site-packages/grpc/_utilities.py", line 162, in result
    self._block(timeout)
  File "/home/<USER>/anaconda3/envs/env_name/lib/python3.9/site-packages/grpc/_utilities.py", line 106, in _block
    raise grpc.FutureTimeoutError()
grpc.FutureTimeoutError

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/zte-ibo-acm-productretrieve/utils/milvus_util/milvus_helpers.py", line 14, in __init__
    connections.connect(host=config['Milvus']['MILVUS_HOST'], port=config['Milvus']['MILVUS_PORT'])
  File "/home/<USER>/anaconda3/envs/env_name/lib/python3.9/site-packages/pymilvus/orm/connections.py", line 422, in connect
    connect_milvus(**kwargs, user=user, password=password, token=token, db_name=db_name)
  File "/home/<USER>/anaconda3/envs/env_name/lib/python3.9/site-packages/pymilvus/orm/connections.py", line 373, in connect_milvus
    gh._wait_for_channel_ready(timeout=timeout)
  File "/home/<USER>/anaconda3/envs/env_name/lib/python3.9/site-packages/pymilvus/client/grpc_handler.py", line 147, in _wait_for_channel_ready
    raise MilvusException(
pymilvus.exceptions.MilvusException: <MilvusException: (code=2, message=Fail connecting to server on **************:19530, illegal connection params or server unavailable)>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/anaconda3/envs/env_name/lib/python3.9/site-packages/flask/app.py", line 2190, in wsgi_app
    response = self.full_dispatch_request()
  File "/home/<USER>/anaconda3/envs/env_name/lib/python3.9/site-packages/flask/app.py", line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "/home/<USER>/anaconda3/envs/env_name/lib/python3.9/site-packages/flask_cors/extension.py", line 194, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
  File "/home/<USER>/anaconda3/envs/env_name/lib/python3.9/site-packages/flask/app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "/home/<USER>/anaconda3/envs/env_name/lib/python3.9/site-packages/flask/app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "/home/<USER>/Desktop/zte-ibo-acm-productretrieve/controller/zxtech_controller.py", line 37, in product_chat
    return _zxtech_(query,XEmpNo,history,rewriteText,g=g_copy)
  File "/home/<USER>/Desktop/zte-ibo-acm-productretrieve/service/zxtech_service.py", line 101, in __call__
    es_entity, es_res_doc, milvus_res, kg_res, rerank_res, map_dict = self.deal_product_doc(list_doc_set_new, query,
  File "/home/<USER>/Desktop/zte-ibo-acm-productretrieve/service/zxtech_service.py", line 340, in deal_product_doc
    milvus_res = self.milvus_recall.query_data_milvus(embed_query, list_doc_set_new)
  File "/home/<USER>/Desktop/zte-ibo-acm-productretrieve/retrieve/milvus_recall.py", line 13, in query_data_milvus
    milvushelper = MilvusHelper()
  File "/home/<USER>/Desktop/zte-ibo-acm-productretrieve/utils/milvus_util/milvus_helpers.py", line 18, in __init__
    raise Exception(f"Failed to connect Milvus: {e}")
Exception: Failed to connect Milvus: <MilvusException: (code=2, message=Fail connecting to server on **************:19530, illegal connection params or server unavailable)>
2025-07-29 16:51:10,770 ｜ INFO ｜ _internal.py ｜ _log ｜ 97 ｜ 127.0.0.1 - - [29/Jul/2025 16:51:10] "[35m[1mPOST /zte-ibo-acm-productretrieve/faq HTTP/1.1[0m" 500 -
2025-07-29 16:56:42,125 ｜ INFO ｜ kafka.py ｜ close ｜ 486 ｜ Closing the Kafka producer with 0 secs timeout.
2025-07-29 16:56:42,126 ｜ INFO ｜ kafka.py ｜ close ｜ 502 ｜ Proceeding to force close the producer since pending requests could not be completed within timeout 0.
2025-07-29 16:56:42,127 ｜ INFO ｜ conn.py ｜ close ｜ 673 ｜ <BrokerConnection node_id=0 host=************:9092 <connected> [IPv4 ('************', 9092)]>: Closing connection. 
2025-07-29 16:59:09,112 ｜ INFO ｜ _internal.py ｜ _log ｜ 97 ｜ [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:7567
 * Running on http://*************:7567
2025-07-29 16:59:09,112 ｜ INFO ｜ _internal.py ｜ _log ｜ 97 ｜ [33mPress CTRL+C to quit[0m
2025-07-29 16:59:28,698 ｜ INFO ｜ _internal.py ｜ _log ｜ 97 ｜ 127.0.0.1 - - [29/Jul/2025 16:59:28] "POST /zte-ibo-acm-productretrieve/faq HTTP/1.1" 200 -
2025-07-29 17:00:11,540 ｜ ERROR ｜ milvus_helpers.py ｜ __init__ ｜ 17 ｜ Failed to connect Milvus: <MilvusException: (code=2, message=Fail connecting to server on ************:19530, illegal connection params or server unavailable)>
2025-07-29 17:00:11,541 ｜ ERROR ｜ app.py ｜ log_exception ｜ 1414 ｜ Exception on /zte-ibo-acm-productretrieve/faq [POST]
Traceback (most recent call last):
  File "/home/<USER>/anaconda3/envs/env_name/lib/python3.9/site-packages/pymilvus/client/grpc_handler.py", line 144, in _wait_for_channel_ready
    grpc.channel_ready_future(self._channel).result(timeout=timeout)
  File "/home/<USER>/anaconda3/envs/env_name/lib/python3.9/site-packages/grpc/_utilities.py", line 162, in result
    self._block(timeout)
  File "/home/<USER>/anaconda3/envs/env_name/lib/python3.9/site-packages/grpc/_utilities.py", line 106, in _block
    raise grpc.FutureTimeoutError()
grpc.FutureTimeoutError

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/zte-ibo-acm-productretrieve/utils/milvus_util/milvus_helpers.py", line 14, in __init__
    connections.connect(host=config['Milvus']['MILVUS_HOST'], port=config['Milvus']['MILVUS_PORT'])
  File "/home/<USER>/anaconda3/envs/env_name/lib/python3.9/site-packages/pymilvus/orm/connections.py", line 422, in connect
    connect_milvus(**kwargs, user=user, password=password, token=token, db_name=db_name)
  File "/home/<USER>/anaconda3/envs/env_name/lib/python3.9/site-packages/pymilvus/orm/connections.py", line 373, in connect_milvus
    gh._wait_for_channel_ready(timeout=timeout)
  File "/home/<USER>/anaconda3/envs/env_name/lib/python3.9/site-packages/pymilvus/client/grpc_handler.py", line 147, in _wait_for_channel_ready
    raise MilvusException(
pymilvus.exceptions.MilvusException: <MilvusException: (code=2, message=Fail connecting to server on ************:19530, illegal connection params or server unavailable)>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/anaconda3/envs/env_name/lib/python3.9/site-packages/flask/app.py", line 2190, in wsgi_app
    response = self.full_dispatch_request()
  File "/home/<USER>/anaconda3/envs/env_name/lib/python3.9/site-packages/flask/app.py", line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "/home/<USER>/anaconda3/envs/env_name/lib/python3.9/site-packages/flask_cors/extension.py", line 194, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
  File "/home/<USER>/anaconda3/envs/env_name/lib/python3.9/site-packages/flask/app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "/home/<USER>/anaconda3/envs/env_name/lib/python3.9/site-packages/flask/app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "/home/<USER>/Desktop/zte-ibo-acm-productretrieve/controller/zxtech_controller.py", line 37, in product_chat
    return _zxtech_(query,XEmpNo,history,rewriteText,g=g_copy)
  File "/home/<USER>/Desktop/zte-ibo-acm-productretrieve/service/zxtech_service.py", line 101, in __call__
    es_entity, es_res_doc, milvus_res, kg_res, rerank_res, map_dict = self.deal_product_doc(list_doc_set_new, query,
  File "/home/<USER>/Desktop/zte-ibo-acm-productretrieve/service/zxtech_service.py", line 340, in deal_product_doc
    milvus_res = self.milvus_recall.query_data_milvus(embed_query, list_doc_set_new)
  File "/home/<USER>/Desktop/zte-ibo-acm-productretrieve/retrieve/milvus_recall.py", line 13, in query_data_milvus
    milvushelper = MilvusHelper()
  File "/home/<USER>/Desktop/zte-ibo-acm-productretrieve/utils/milvus_util/milvus_helpers.py", line 18, in __init__
    raise Exception(f"Failed to connect Milvus: {e}")
Exception: Failed to connect Milvus: <MilvusException: (code=2, message=Fail connecting to server on ************:19530, illegal connection params or server unavailable)>
2025-07-29 17:00:11,552 ｜ INFO ｜ _internal.py ｜ _log ｜ 97 ｜ 127.0.0.1 - - [29/Jul/2025 17:00:11] "[35m[1mPOST /zte-ibo-acm-productretrieve/faq HTTP/1.1[0m" 500 -
2025-07-29 17:07:11,268 ｜ INFO ｜ _internal.py ｜ _log ｜ 97 ｜ [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:7567
 * Running on http://*************:7567
2025-07-29 17:07:11,269 ｜ INFO ｜ _internal.py ｜ _log ｜ 97 ｜ [33mPress CTRL+C to quit[0m
2025-07-29 17:07:28,317 ｜ INFO ｜ _internal.py ｜ _log ｜ 97 ｜ ************* - - [29/Jul/2025 17:07:28] "POST /zte-ibo-acm-productretrieve/faq HTTP/1.1" 200 -
2025-07-29 17:09:43,613 ｜ INFO ｜ _internal.py ｜ _log ｜ 97 ｜ [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:7567
 * Running on http://*************:7567
2025-07-29 17:09:43,613 ｜ INFO ｜ _internal.py ｜ _log ｜ 97 ｜ [33mPress CTRL+C to quit[0m
2025-07-29 17:10:35,280 ｜ INFO ｜ _internal.py ｜ _log ｜ 97 ｜ ************* - - [29/Jul/2025 17:10:35] "POST /zte-ibo-acm-productretrieve/faq HTTP/1.1" 200 -
2025-07-29 17:18:32,248 ｜ INFO ｜ _internal.py ｜ _log ｜ 97 ｜ [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:7567
 * Running on http://*************:7567
2025-07-29 17:18:32,249 ｜ INFO ｜ _internal.py ｜ _log ｜ 97 ｜ [33mPress CTRL+C to quit[0m
2025-07-29 17:18:39,343 ｜ INFO ｜ _internal.py ｜ _log ｜ 97 ｜ ************* - - [29/Jul/2025 17:18:39] "POST /zte-ibo-acm-productretrieve/faq HTTP/1.1" 200 -
2025-07-29 17:24:48,010 ｜ INFO ｜ _internal.py ｜ _log ｜ 97 ｜ [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:7567
 * Running on http://*************:7567
2025-07-29 17:24:48,010 ｜ INFO ｜ _internal.py ｜ _log ｜ 97 ｜ [33mPress CTRL+C to quit[0m
2025-07-29 17:24:56,857 ｜ INFO ｜ _internal.py ｜ _log ｜ 97 ｜ ************* - - [29/Jul/2025 17:24:56] "POST /zte-ibo-acm-productretrieve/faq HTTP/1.1" 200 -
2025-07-29 17:26:27,444 ｜ INFO ｜ _internal.py ｜ _log ｜ 97 ｜ ************* - - [29/Jul/2025 17:26:27] "POST /zte-ibo-acm-productretrieve/faq HTTP/1.1" 200 -
2025-07-29 17:27:33,531 ｜ INFO ｜ _internal.py ｜ _log ｜ 97 ｜ ************* - - [29/Jul/2025 17:27:33] "POST /zte-ibo-acm-productretrieve/faq HTTP/1.1" 200 -
2025-07-29 17:28:38,583 ｜ INFO ｜ _internal.py ｜ _log ｜ 97 ｜ [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:7567
 * Running on http://*************:7567
2025-07-29 17:28:38,583 ｜ INFO ｜ _internal.py ｜ _log ｜ 97 ｜ [33mPress CTRL+C to quit[0m
2025-07-29 17:28:45,309 ｜ INFO ｜ _internal.py ｜ _log ｜ 97 ｜ ************* - - [29/Jul/2025 17:28:45] "POST /zte-ibo-acm-productretrieve/faq HTTP/1.1" 200 -
2025-07-29 17:30:10,052 ｜ INFO ｜ _internal.py ｜ _log ｜ 97 ｜ [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:7567
 * Running on http://*************:7567
2025-07-29 17:30:10,052 ｜ INFO ｜ _internal.py ｜ _log ｜ 97 ｜ [33mPress CTRL+C to quit[0m
2025-07-29 17:30:17,725 ｜ INFO ｜ _internal.py ｜ _log ｜ 97 ｜ ************* - - [29/Jul/2025 17:30:17] "POST /zte-ibo-acm-productretrieve/faq HTTP/1.1" 200 -
2025-07-29 21:33:53,801 ｜ INFO ｜ _internal.py ｜ _log ｜ 97 ｜ ************* - - [29/Jul/2025 21:33:53] "POST /zte-ibo-acm-productretrieve/faq HTTP/1.1" 200 -
2025-07-29 21:35:06,065 ｜ INFO ｜ _internal.py ｜ _log ｜ 97 ｜ [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:7567
 * Running on http://*************:7567
2025-07-29 21:35:06,065 ｜ INFO ｜ _internal.py ｜ _log ｜ 97 ｜ [33mPress CTRL+C to quit[0m
2025-07-29 21:35:12,077 ｜ INFO ｜ _internal.py ｜ _log ｜ 97 ｜ ************* - - [29/Jul/2025 21:35:12] "POST /zte-ibo-acm-productretrieve/faq HTTP/1.1" 200 -
2025-07-29 21:35:50,229 ｜ INFO ｜ _internal.py ｜ _log ｜ 97 ｜ [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:7567
 * Running on http://*************:7567
2025-07-29 21:35:50,229 ｜ INFO ｜ _internal.py ｜ _log ｜ 97 ｜ [33mPress CTRL+C to quit[0m
2025-07-29 21:36:05,311 ｜ INFO ｜ _internal.py ｜ _log ｜ 97 ｜ ************* - - [29/Jul/2025 21:36:05] "POST /zte-ibo-acm-productretrieve/faq HTTP/1.1" 200 -
2025-07-29 21:36:26,226 ｜ INFO ｜ _internal.py ｜ _log ｜ 97 ｜ ************* - - [29/Jul/2025 21:36:26] "POST /zte-ibo-acm-productretrieve/faq HTTP/1.1" 200 -
2025-07-29 21:40:41,806 ｜ INFO ｜ _internal.py ｜ _log ｜ 97 ｜ ************* - - [29/Jul/2025 21:40:41] "POST /zte-ibo-acm-productretrieve/faq HTTP/1.1" 200 -
2025-07-29 21:41:42,922 ｜ INFO ｜ _internal.py ｜ _log ｜ 97 ｜ [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:7567
 * Running on http://*************:7567
2025-07-29 21:41:42,922 ｜ INFO ｜ _internal.py ｜ _log ｜ 97 ｜ [33mPress CTRL+C to quit[0m
2025-07-29 21:41:50,336 ｜ INFO ｜ _internal.py ｜ _log ｜ 97 ｜ ************* - - [29/Jul/2025 21:41:50] "POST /zte-ibo-acm-productretrieve/faq HTTP/1.1" 200 -
2025-07-29 21:42:10,861 ｜ INFO ｜ _internal.py ｜ _log ｜ 97 ｜ ************* - - [29/Jul/2025 21:42:10] "POST /zte-ibo-acm-productretrieve/faq HTTP/1.1" 200 -
2025-07-29 21:42:28,140 ｜ INFO ｜ _internal.py ｜ _log ｜ 97 ｜ ************* - - [29/Jul/2025 21:42:28] "POST /zte-ibo-acm-productretrieve/faq HTTP/1.1" 200 -
2025-07-29 21:42:58,289 ｜ INFO ｜ _internal.py ｜ _log ｜ 97 ｜ [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:7567
 * Running on http://*************:7567
2025-07-29 21:42:58,289 ｜ INFO ｜ _internal.py ｜ _log ｜ 97 ｜ [33mPress CTRL+C to quit[0m
2025-07-29 21:43:04,467 ｜ INFO ｜ _internal.py ｜ _log ｜ 97 ｜ ************* - - [29/Jul/2025 21:43:04] "POST /zte-ibo-acm-productretrieve/faq HTTP/1.1" 200 -
2025-07-29 21:43:21,915 ｜ INFO ｜ _internal.py ｜ _log ｜ 97 ｜ ************* - - [29/Jul/2025 21:43:21] "POST /zte-ibo-acm-productretrieve/faq HTTP/1.1" 200 -
2025-07-29 21:45:09,788 ｜ INFO ｜ _internal.py ｜ _log ｜ 97 ｜ ************* - - [29/Jul/2025 21:45:09] "POST /zte-ibo-acm-productretrieve/faq HTTP/1.1" 200 -
2025-07-29 22:59:59,917 ｜ INFO ｜ _internal.py ｜ _log ｜ 97 ｜ [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:7567
 * Running on http://*************:7567
2025-07-29 22:59:59,918 ｜ INFO ｜ _internal.py ｜ _log ｜ 97 ｜ [33mPress CTRL+C to quit[0m
2025-07-29 23:00:06,489 ｜ INFO ｜ _internal.py ｜ _log ｜ 97 ｜ ************* - - [29/Jul/2025 23:00:06] "POST /zte-ibo-acm-productretrieve/faq HTTP/1.1" 200 -
2025-07-29 23:00:24,179 ｜ INFO ｜ _internal.py ｜ _log ｜ 97 ｜ ************* - - [29/Jul/2025 23:00:24] "POST /zte-ibo-acm-productretrieve/faq HTTP/1.1" 200 -
2025-07-29 23:00:49,979 ｜ INFO ｜ _internal.py ｜ _log ｜ 97 ｜ ************* - - [29/Jul/2025 23:00:49] "POST /zte-ibo-acm-productretrieve/faq HTTP/1.1" 200 -
