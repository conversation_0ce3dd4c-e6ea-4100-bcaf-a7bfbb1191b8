import numpy as np
import hashlib
from utils.logger.logger_util import logger


class MockGetEmbedding:
    """
    Mock embedding服务，用于在无法连接外部API时提供模拟的向量表示
    """

    def __init__(self, config):
        self.embedding_url = config['Embedding']['url']
        self.embedding_dim = 1024  # BGE-M3的向量维度

    def get_embedding(self, question):
        """模拟获取embedding向量"""
        try:
            # 基于文本内容生成确定性的mock向量
            result = self._generate_mock_vector(question)
            logger.info(f"Mock embedding generated for text: {question[:50]}...")
            return result
        except Exception as e:
            logger.error(f"Mock embedding接口报错: {str(e)}")
            return None

    def post_url_m3(self, query):
        """模拟BGE-M3 API调用"""
        query = self.deal_query(query, 8000)
        if isinstance(query, str):
            query = [query]
        
        try:
            # 为每个查询生成mock向量
            result = []
            for q in query:
                mock_vector = self._generate_mock_vector(q)
                result.append(mock_vector)
            
            logger.info(f"Mock BGE-M3 embedding generated for {len(query)} queries")
            return result
        except Exception as e:
            logger.error(f"Mock embedding响应报错：{str(e)}")
            return None

    def _generate_mock_vector(self, text):
        """
        基于文本内容生成确定性的mock向量
        使用哈希函数确保相同文本生成相同向量
        """
        # 使用文本的MD5哈希作为随机种子
        text_hash = hashlib.md5(text.encode('utf-8')).hexdigest()
        seed = int(text_hash[:8], 16)
        
        # 设置随机种子以确保结果可重现
        np.random.seed(seed % (2**32))
        
        # 生成随机向量并标准化
        vector = np.random.normal(0, 1, self.embedding_dim)
        # L2标准化
        vector = vector / np.linalg.norm(vector)
        
        return vector.tolist()

    def deal_query(self, query, length_broad: int = 8000):
        """
        处理查询字符串或列表中的字符串，确保每个字符串的长度不超过指定的最大长度。
        """
        if isinstance(query, str):
            return query[:length_broad]
        elif isinstance(query, list):
            return [s[:length_broad] if isinstance(s, str) else s for s in query]
        else:
            return query