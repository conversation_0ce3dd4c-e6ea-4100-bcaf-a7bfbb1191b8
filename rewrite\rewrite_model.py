import json
import logging
import re

import threading

import requests

from domain.constants.Enums import Targets, Rewrite, PATTERNEnum
from llm.LLMResult import rewrite_with_llm, parse_llm_response
from prompt.PromptLoader import PromptLoader
from rewrite import MAX_HISTORY_LENGTH
from utils.logger.log_wrapper import wrap_log_content, get_extra
from utils.logger.logger_util import logger, rewrite_logger

## 在这设置配置
from domain.config.zxtech_config import config


# Started by AICoder, pid:2cf06pf4a2jdf3c14f6008de20db0f2581d2bc0e
def format_json_data(json_data):
    # 初始化查询和答案列表
    queries = []
    answers = []

    # 解析JSON数据，并根据角色将内容添加到相应的列表中
    for item in json_data:
        if item["role"] == "user":
            queries.append(item["content"])
        if item["role"] == "assistant":
            answers.append(item["content"])

    # 初始化输出字符串
    output = ""

    # 使用zip函数同时遍历查询和答案列表，并格式化输出
    for i, (query, answer) in enumerate(zip(queries, answers), start=1):
        output += f"query{i}: {query}\n"
        output += f"answer{i}: {answer}\n\n"

    # 返回格式化后的字符串
    return output

# Ended by AICoder, pid:2cf06pf4a2jdf3c14f6008de20db0f2581d2bc0e

def extract_content(history):
    result = []
    for entry in history:
        if entry["role"] == "assistant":
            if Rewrite.FILTER.value in entry["content"]:
                parts = entry["content"].split(Rewrite.FILTER.value)
                entry["content"] = parts[0].strip()
            result.append(entry)
        else:
            result.append(entry)
    return result


def handle_related(user_input, history, time_record_dict=None, g=None):
    # 调用函数
    if g is None:
        g = {}
    history = extract_content(history)
    # history[::2], history[1::2] = history[1::2], history[::2]
    if isinstance(history, list):
        history = format_json_data(history)
    generate_similar_question_prompt = PromptLoader(user_content="", question=user_input,
                                                    target=Targets.REWRITE, history=history).get_prompt()
    messages = [
        {
            "role": "system",
            "content": generate_similar_question_prompt['sys_prompt']
        },
        {
            "role": "user",
            "content": generate_similar_question_prompt['user_prompt']
        }
    ]
    questions_list = rewrite_with_llm("", config['LLM'], messages, time_record_dict)
    processed_questions_list = questions_list
    rewrite_log_content = {"input": messages, "output": questions_list,
                           "processed_output": processed_questions_list}
    rewrite_logger.info(json.dumps(rewrite_log_content,ensure_ascii=False))
    logger.info(wrap_log_content(f'原问题：{user_input}\n改写后的问题：{questions_list}\n改写后过滤的问题：{processed_questions_list}', g), extra=get_extra({'log_type': 'rewrite'}))
    if processed_questions_list:
        return processed_questions_list.strip()
    else:
        logger.warn(wrap_log_content("改写后过滤的问题为空", g))
        return user_input


def filter_chat_messages(chat_messages):
    if len(chat_messages) > MAX_HISTORY_LENGTH:
        return chat_messages[-MAX_HISTORY_LENGTH:-1]
    else:
        return chat_messages[:-1]

# Started by AICoder, pid:b3c1e91978ac58014d2b0967f0be1d4482e4ba10
def async_rewrite(user_input, history):
    """异步重写函数"""
# Started by AICoder, pid:h3d7dbe29cm645014ebb0bbe2001750d8753f87a
# 如果 history 是列表类型，则格式化 JSON 数据
    if isinstance(history, list):
        history = format_json_data(history)
# Ended by AICoder, pid:h3d7dbe29cm645014ebb0bbe2001750d8753f87a
    # 获取prompt
    prompt = PromptLoader(user_content="", question=user_input, target=Targets.REWRITE_GLM,
                          history=history).get_prompt()
    # 获取LLM配置
    LLM_config = config['LLM']
    # 构造请求体
    url = LLM_config['rewrite_model_url_test']
    messages = [
        {
            "role": "system",
            "content": prompt['sys_prompt']
        },
        {
            "role": "user",
            "content": prompt['user_prompt']
        }
    ]
    body = {
        "stream": False,
        "model": LLM_config['rewrite_model_name_test'],
        "messages": messages,
        "temperature": LLM_config['temperature'],
        "top_k": LLM_config['top_k'],
        "top_p": LLM_config['top_p'],
        "presence_penalty": LLM_config['presence_penalty'],
        "frequency_penalty": LLM_config['frequency_penalty'],
    }
    try:
        # 发送POST请求
        response = requests.post(url, json=body, verify=True)
        # 解析响应
        res_dict = response.json()
        res = parse_llm_response(res_dict, 'openai')
        # 记录日志
        log_dict = {"id": "glm", "input": messages, "output": res}
        rewrite_logger.info(json.dumps(log_dict, ensure_ascii=False))
    except Exception as e:
        logger.info('async_rewrite_error', e)


def run_async_task(user_input, history):
    """运行异步任务"""
    # 创建线程并启动
    thread = threading.Thread(target=async_rewrite, args=(user_input, history))
    thread.start()

# Ended by AICoder, pid:b3c1e91978ac58014d2b0967f0be1d4482e4ba10

