#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import re
from datetime import datetime
import json

def parse_log_entry(line):
    """解析日志条目"""
    # 日志格式分析 - 使用制表符分割
    parts = line.strip().split('\t')
    if len(parts) < 20:
        return None
    
    try:
        return {
            'app': parts[0],
            'level': parts[1], 
            'timestamp': parts[13],
            'message': parts[41] if len(parts) > 41 else '',
            'raw_line': line
        }
    except IndexError:
        return None

def extract_error_details(message):
    """提取错误详情"""
    if not message:
        return None
        
    error_info = {}
    
    # 提取主要错误类型
    if 'ProxyError' in message:
        error_info['type'] = 'ProxyError'
        error_info['category'] = '网络代理错误'
    elif 'ConnectionError' in message:
        error_info['type'] = 'ConnectionError' 
        error_info['category'] = '连接错误'
    elif 'Timeout' in message:
        error_info['type'] = 'Timeout'
        error_info['category'] = '超时错误'
    
    # 提取目标URL
    url_match = re.search(r'url: ([^\s\)]+)', message)
    if url_match:
        error_info['target_url'] = url_match.group(1)
    
    # 提取主机信息
    host_match = re.search(r"host='([^']+)'", message)
    if host_match:
        error_info['target_host'] = host_match.group(1)
        
    # 提取端口信息
    port_match = re.search(r'port=(\d+)', message)
    if port_match:
        error_info['target_port'] = port_match.group(1)
    
    # 提取源代码位置
    file_match = re.search(r'File "([^"]+)", line (\d+)', message)
    if file_match:
        error_info['source_file'] = file_match.group(1)
        error_info['source_line'] = file_match.group(2)
    
    return error_info

def analyze_kg_es_errors():
    """分析KG和ES相关错误"""
    log_file = "resource/logs/log_2025-08-01.log"
    
    if not os.path.exists(log_file):
        print("❌ 日志文件不存在")
        return
    
    print("🔍 分析KG和ES服务连接错误")
    print("=" * 60)
    
    kg_es_errors = []
    other_errors = []
    
    with open(log_file, 'r', encoding='utf-8') as f:
        for line_num, line in enumerate(f, 1):
            entry = parse_log_entry(line)
            if not entry or entry['level'] != 'ERROR':
                continue
                
            error_details = extract_error_details(entry['message'])
            if error_details:
                entry['error_details'] = error_details
                
                # 判断是否为KG/ES相关错误
                target_host = error_details.get('target_host', '')
                target_url = error_details.get('target_url', '')
                
                if ('kger.zte.com.cn' in target_host or 
                    'kgbff' in target_url or 
                    'es/commonSearch' in target_url):
                    kg_es_errors.append(entry)
                else:
                    other_errors.append(entry)
    
    # 输出KG/ES错误分析
    if kg_es_errors:
        print(f"📊 发现 {len(kg_es_errors)} 个KG/ES相关错误:")
        print()
        
        for i, error in enumerate(kg_es_errors, 1):
            details = error['error_details']
            print(f"🔸 错误 #{i}")
            print(f"   时间: {error['timestamp']}")
            print(f"   类型: {details.get('category', '未知')}")
            print(f"   目标主机: {details.get('target_host', '未知')}")
            print(f"   目标端口: {details.get('target_port', '未知')}")
            print(f"   请求URL: {details.get('target_url', '未知')}")
            
            if details.get('source_file'):
                filename = os.path.basename(details['source_file'])
                print(f"   源码位置: {filename}:{details.get('source_line', '未知')}")
            
            print()
    else:
        print("✅ 未发现KG/ES相关的详细错误日志")
    
    # 输出其他错误
    if other_errors:
        print(f"📋 其他错误 ({len(other_errors)} 个):")
        for error in other_errors:
            details = error.get('error_details', {})
            print(f"   - {details.get('category', '未知错误')}: {details.get('target_host', '未知主机')}")
    
    return kg_es_errors

def show_recent_logs(minutes=10):
    """显示最近几分钟的日志"""
    log_file = "resource/logs/log_2025-08-01.log"
    
    if not os.path.exists(log_file):
        print("❌ 日志文件不存在")
        return
    
    print(f"📋 最近 {minutes} 分钟的日志:")
    print("=" * 60)
    
    recent_entries = []
    current_time = datetime.now()
    
    with open(log_file, 'r', encoding='utf-8') as f:
        for line in f:
            entry = parse_log_entry(line)
            if not entry:
                continue
                
            try:
                # 解析时间戳
                log_time = datetime.strptime(entry['timestamp'].split()[0] + ' ' + entry['timestamp'].split()[1], 
                                           '%Y-%m-%d %H:%M:%S')
                
                # 检查是否在指定时间范围内
                time_diff = (current_time - log_time).total_seconds() / 60
                if time_diff <= minutes:
                    recent_entries.append(entry)
            except:
                continue
                
    # 显示最近的日志条目
    for entry in recent_entries[-20:]:  # 只显示最新的20条
        level_color = {
            'ERROR': '🔴',
            'WARN': '🟡', 
            'INFO': '🔵',
            'DEBUG': '⚪'
        }.get(entry['level'], '⚫')
        
        print(f"{level_color} [{entry['timestamp']}] {entry['level']}")
        if entry['message'] and len(entry['message']) > 0:
            # 只显示消息的前100个字符
            msg = entry['message'][:100] + ('...' if len(entry['message']) > 100 else '')
            print(f"   {msg}")
        print()

def main():
    """主函数"""
    print("KG/ES服务日志分析工具")
    print("=" * 60)
    print()
    
    # 分析KG/ES错误
    kg_es_errors = analyze_kg_es_errors()
    
    print()
    print("=" * 60)
    
    # 显示最近日志
    show_recent_logs(30)
    
    # 提供解决建议
    if kg_es_errors:
        print()
        print("解决建议:")
        print("1. 网络代理问题 - 检查公司代理设置")
        print("2. 服务不可用 - 确认kger.zte.com.cn服务状态") 
        print("3. 防火墙限制 - 检查443端口是否开放")
        print("4. 开发环境 - 考虑使用mock服务替代")

if __name__ == "__main__":
    main()