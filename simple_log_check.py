#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import re

def extract_kg_es_errors():
    """提取KG和ES相关错误"""
    log_file = "resource/logs/log_2025-08-01.log"
    
    print("KG/ES服务连接错误分析")
    print("=" * 50)
    
    try:
        with open(log_file, 'r', encoding='utf-8') as f:
            content = f.read()
            
        # 查找包含错误堆栈的行
        error_entries = []
        lines = content.split('\n')
        
        for i, line in enumerate(lines):
            if 'ERROR' in line and ('kger.zte.com.cn' in line or 'es/commonSearch' in line):
                error_entries.append((i+1, line))
        
        if error_entries:
            print(f"发现 {len(error_entries)} 个KG/ES相关错误:")
            print()
            
            for line_num, line in error_entries:
                print(f"行 {line_num}:")
                
                # 提取时间戳 - 第14个字段
                parts = line.split('\t')
                if len(parts) > 13:
                    timestamp = parts[13]
                    print(f"时间: {timestamp}")
                
                # 提取错误消息 - 最后一个有内容的字段
                if len(parts) > 41 and parts[41]:
                    error_msg = parts[41]
                    
                    # 提取关键信息
                    if 'kger.zte.com.cn' in error_msg:
                        print("目标主机: kger.zte.com.cn")
                        print("端口: 443")
                    
                    if 'es/commonSearch' in error_msg:
                        print("请求URL: /zte-dmt-dmp-kgbff/zte-dmt-dmp-userprofile-bff-new/es/commonSearch")
                        
                    if 'ProxyError' in error_msg:
                        print("错误类型: 网络代理错误")
                        
                    if 'Tunnel connection failed: 302 Moved' in error_msg:
                        print("具体错误: 代理隧道连接失败 (302 重定向)")
                    
                    # 提取源代码位置
                    file_match = re.search(r'File "([^"]+retrieve[^"]*)", line (\d+)', error_msg)
                    if file_match:
                        import os
                        filename = os.path.basename(file_match.group(1))
                        line_no = file_match.group(2)
                        print(f"源码位置: {filename}:{line_no}")
                
                print("-" * 30)
                print()
        else:
            print("未发现KG/ES相关的详细错误日志")
            
    except FileNotFoundError:
        print("错误: 日志文件不存在")
    except Exception as e:
        print(f"分析日志时出错: {str(e)}")

if __name__ == "__main__":
    extract_kg_es_errors()