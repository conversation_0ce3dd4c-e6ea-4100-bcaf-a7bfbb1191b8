#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json
import os
import time
from datetime import datetime

# 禁用代理
os.environ['no_proxy'] = '127.0.0.1,localhost'
proxies = {'http': None, 'https': None}

def print_separator(title=""):
    """打印分隔线"""
    if title:
        print(f"\n{'='*20} {title} {'='*20}")
    else:
        print("="*60)

def print_result(success, message, details=None):
    """打印测试结果"""
    status = "✓" if success else "✗"
    color = "\033[92m" if success else "\033[91m"  # 绿色或红色
    reset = "\033[0m"

    print(f"{color}{status} {message}{reset}")
    if details:
        print(f"   详情: {details}")

def test_health_check():
    """测试健康检查接口"""
    print_separator("健康检查接口测试")

    try:
        start_time = time.time()
        response = requests.get("http://127.0.0.1:7567/zte-ibo-acm-productretrieve/info",
                              proxies=proxies, timeout=10)
        end_time = time.time()

        print(f"请求URL: http://127.0.0.1:7567/zte-ibo-acm-productretrieve/info")
        print(f"响应时间: {(end_time - start_time)*1000:.2f}ms")
        print(f"状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        print(f"响应内容: {response.text}")

        if response.status_code == 200:
            try:
                json_response = response.json()
                if json_response.get("status") == "ok":
                    print_result(True, "健康检查接口正常")
                else:
                    print_result(False, "健康检查接口响应异常", json_response)
            except json.JSONDecodeError:
                print_result(False, "响应不是有效的JSON格式", response.text)
        else:
            print_result(False, f"健康检查接口失败，状态码: {response.status_code}")

    except requests.exceptions.Timeout:
        print_result(False, "健康检查接口超时")
    except requests.exceptions.ConnectionError:
        print_result(False, "无法连接到服务器，请确认服务是否启动")
    except Exception as e:
        print_result(False, f"健康检查接口异常: {str(e)}")

def test_faq_api():
    """测试问答接口"""
    print_separator("问答接口测试")

    url = "http://127.0.0.1:7567/zte-ibo-acm-productretrieve/faq"
    headers = {
        'X-Emp-No': '0668001399',
        'X-Auth-Value': 'a9429deaab5e7bcd66770610617d41a2',
        'Content-Type': 'application/json'
    }
    data = {
        "text": "M6000-8S支持哪些接口"
    }

    print(f"请求URL: {url}")
    print(f"请求头: {json.dumps(headers, indent=2, ensure_ascii=False)}")
    print(f"请求体: {json.dumps(data, indent=2, ensure_ascii=False)}")

    try:
        start_time = time.time()
        response = requests.post(url, headers=headers, json=data,
                               proxies=proxies, timeout=60)
        end_time = time.time()

        print(f"响应时间: {(end_time - start_time)*1000:.2f}ms")
        print(f"状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")

        if response.status_code == 200:
            try:
                json_response = response.json()
                print_result(True, "问答接口调用成功")
                print("响应内容:")
                print(json.dumps(json_response, indent=2, ensure_ascii=False))
            except json.JSONDecodeError:
                print_result(False, "响应不是有效的JSON格式")
                print(f"原始响应: {response.text}")
        else:
            print_result(False, f"问答接口失败，状态码: {response.status_code}")
            print(f"错误响应: {response.text}")

    except requests.exceptions.Timeout:
        print_result(False, "问答接口请求超时")
    except requests.exceptions.ConnectionError:
        print_result(False, "无法连接到服务器")
    except Exception as e:
        print_result(False, f"问答接口异常: {str(e)}")

def test_error_cases():
    """测试错误情况"""
    print_separator("错误情况测试")

    base_url = "http://127.0.0.1:7567/zte-ibo-acm-productretrieve/faq"

    # 测试1: 缺少必要头部
    print("\n1. 测试缺少X-Emp-No头部:")
    try:
        response = requests.post(
            base_url,
            headers={'Content-Type': 'application/json'},
            json={"text": "测试问题"},
            proxies=proxies,
            timeout=10
        )
        print(f"状态码: {response.status_code}")
        if response.status_code == 500:
            print_result(True, "正确返回500错误（缺少必要头部）")
        else:
            print_result(False, f"期望500错误，实际返回: {response.status_code}")
            print(f"响应: {response.text}")
    except Exception as e:
        print_result(False, f"测试异常: {str(e)}")

    # 测试2: 空输入
    print("\n2. 测试空输入:")
    try:
        response = requests.post(
            base_url,
            headers={
                'X-Emp-No': '0668001399',
                'X-Auth-Value': 'a9429deaab5e7bcd66770610617d41a2',
                'Content-Type': 'application/json'
            },
            json={"text": ""},
            proxies=proxies,
            timeout=10
        )
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            try:
                json_response = response.json()
                if json_response.get("code") == "0004":
                    print_result(True, "正确返回空输入错误")
                else:
                    print_result(False, "未正确处理空输入", json_response)
            except:
                print_result(False, "空输入响应格式异常", response.text)
        else:
            print_result(False, f"空输入测试失败，状态码: {response.status_code}")
    except Exception as e:
        print_result(False, f"空输入测试异常: {str(e)}")

    # 测试3: 无效JSON
    print("\n3. 测试无效JSON:")
    try:
        response = requests.post(
            base_url,
            headers={
                'X-Emp-No': '0668001399',
                'X-Auth-Value': 'a9429deaab5e7bcd66770610617d41a2',
                'Content-Type': 'application/json'
            },
            data="invalid json",
            proxies=proxies,
            timeout=10
        )
        print(f"状态码: {response.status_code}")
        if response.status_code == 400 or response.status_code == 500:
            print_result(True, "正确处理无效JSON")
        else:
            print_result(False, f"无效JSON处理异常，状态码: {response.status_code}")
    except Exception as e:
        print_result(False, f"无效JSON测试异常: {str(e)}")

def test_performance():
    """性能测试"""
    print_separator("性能测试")

    url = "http://127.0.0.1:7567/zte-ibo-acm-productretrieve/info"

    print("执行10次健康检查接口调用...")
    times = []
    success_count = 0

    for i in range(10):
        try:
            start_time = time.time()
            response = requests.get(url, proxies=proxies, timeout=5)
            end_time = time.time()

            response_time = (end_time - start_time) * 1000
            times.append(response_time)

            if response.status_code == 200:
                success_count += 1

            print(f"第{i+1}次: {response_time:.2f}ms - {'成功' if response.status_code == 200 else '失败'}")

        except Exception as e:
            print(f"第{i+1}次: 失败 - {str(e)}")

    if times:
        avg_time = sum(times) / len(times)
        min_time = min(times)
        max_time = max(times)

        print(f"\n性能统计:")
        print(f"成功率: {success_count}/10 ({success_count*10}%)")
        print(f"平均响应时间: {avg_time:.2f}ms")
        print(f"最快响应时间: {min_time:.2f}ms")
        print(f"最慢响应时间: {max_time:.2f}ms")

        if avg_time < 100:
            print_result(True, "响应时间良好")
        elif avg_time < 500:
            print_result(True, "响应时间可接受")
        else:
            print_result(False, "响应时间较慢")

def main():
    """主测试函数"""
    print(f"开始API接口测试 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print_separator()

    # 基础功能测试
    test_health_check()
    test_faq_api()
    test_error_cases()

    # 性能测试
    test_performance()

    print_separator("测试完成")
    print(f"测试结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()