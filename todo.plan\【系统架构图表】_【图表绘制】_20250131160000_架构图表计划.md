# 【系统架构图表】_【图表绘制】_20250131160000_架构图表计划

## 📋 任务概述

基于ZTE智能产品检索系统，使用实例"ZXR10 M6000的端口配置怎么设置？"绘制三个核心图表，目标是让小白用户能够清晰理解系统架构和数据流程。

## 🎯 具体任务

### 1. 系统架构图
- **目标**: 展示系统各层级组件及其关系
- **实例化**: 以ZXR10 M6000查询为例，标注数据流向
- **包含组件**: 
  - 接口层（Flask API）
  - 业务层（Controller + Service）
  - 检索层（ES + Milvus + KG）
  - 算法层（Rerank + LLM）
  - 数据层（向量库 + 知识库）

### 2. 核心数据流程图
- **目标**: 详细展示数据在各模块间的流转过程
- **实例化**: 从"ZXR10 M6000端口配置"问题输入到答案输出的完整数据路径
- **关键步骤**:
  - 查询预处理
  - 实体提取
  - 多路召回
  - 重排序融合
  - LLM生成

### 3. 时序图
- **目标**: 展示具体的调用时序和响应时间
- **实例化**: 完整的API调用链路
- **时间节点**:
  - 用户请求 (0ms)
  - 实体提取 (~100ms)
  - 并行检索 (~500ms)
  - 重排序 (~200ms)
  - LLM生成 (~2000ms)
  - 响应返回 (~2800ms)

## 🔧 技术要求

- 使用Mermaid图表语言
- 中文标注，适合小白理解
- 实例化说明，避免抽象概念
- 包含性能指标和数据量级

## ✅ 成功标准

- 图表清晰易懂，逻辑完整
- 实例化说明具体生动
- 技术细节与业务场景结合
- 小白用户能够快速理解系统工作原理

## 📅 预计耗时

- 系统架构图: 20分钟
- 数据流程图: 25分钟  
- 时序图: 15分钟
- 说明文档: 10分钟
- **总计**: 70分钟