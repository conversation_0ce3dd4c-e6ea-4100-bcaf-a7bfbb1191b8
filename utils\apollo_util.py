import base64
import hashlib
import hmac
import time
import uuid

import requests
import yaml
from utils.encryption_util import decrypt
from apollo.apollo_client import ApolloClient


def _decrypt_data_key(domain,uri,secret,kms_headers):
    url = f"{domain}{uri}"
    print(url)
    x_timestamp = str(int(time.time()))
    x_nonce = str(uuid.uuid4())
    headers = _get_headers(x_timestamp, x_nonce, uri,kms_headers)
    data = {
        "dataKeyCipher": secret["dataKeyCipher"],
        "secretKeyId": secret["secretKeyId"],
    }
    data_key_plain = requests.post(url, headers=headers, json=data).json()['dataKeyPlain']
    return data_key_plain


def _get_authorization(x_timestamp, x_nonce, uri,kms_headers):
    def hmacsha256(key, data):
        mac = hmac.new(key.encode('utf-8'), data.encode('utf-8'), hashlib.sha256)
        return base64.b64encode(mac.digest()).decode('utf-8')

    signing_string = f"{kms_headers['APP_NAME']},{x_timestamp},{x_nonce},{kms_headers['USER_ID']},{uri}"
    print(uri)
    signature = hmacsha256(kms_headers['APP_SECRET'], signing_string)
    APP_NAME = kms_headers['APP_NAME']
    authorization = f'app-hmac username="{APP_NAME}", algorithm="hmac-sha256", headers="x-timestamp x-nonce x-app-user-id", signature="{signature}"'
    return authorization


def _get_headers(x_timestamp, x_nonce, uri,kms_headers):
    authorization = _get_authorization(x_timestamp, x_nonce, uri,kms_headers)
    headers = {
        "Content-Type": "application/json",
        "x-timestamp": x_timestamp,
        "authorization": authorization,
        "x-nonce": x_nonce,
        "x-app-user-id": kms_headers['USER_ID'],

    }
    return headers
