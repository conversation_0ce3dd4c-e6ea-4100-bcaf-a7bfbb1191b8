# ZTE产品智能问答系统 - 部署运维指南

## 📋 概述

本文档提供ZTE产品智能问答系统的完整部署和运维指南，包括环境准备、部署步骤、监控配置、故障排查等内容。

---

## 🛠️ 环境要求

### 系统要求

| 组件 | 最低要求 | 推荐配置 |
|------|----------|----------|
| 操作系统 | Linux/Windows | CentOS 7+ / Ubuntu 18+ |
| Python | 3.8+ | 3.10+ |
| 内存 | 8GB | 16GB+ |
| CPU | 4核 | 8核+ |
| 磁盘 | 100GB | 500GB+ |
| 网络 | 100Mbps | 1Gbps+ |

### 依赖服务

| 服务 | 版本 | 用途 | 必需性 |
|------|------|------|--------|
| Elasticsearch | 7.x+ | 文本检索 | 必需 |
| Milvus | 2.4+ | 向量检索 | 必需 |
| Redis | 6.x+ | 缓存 | 推荐 |
| Apollo | 1.x+ | 配置中心 | 可选 |
| Docker | 20.x+ | 容器化 | 推荐 |

---

## 🚀 部署方式

### 方式1: 本地开发部署

#### 1. 环境准备

```bash
# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate     # Windows

# 升级pip
pip install --upgrade pip
```

#### 2. 安装依赖

```bash
# 使用内网源安装依赖
pip install -r requirements.txt \
  --index-url https://artnj.zte.com.cn/artifactory/api/pypi/public-pypi-virtual/simple \
  --trusted-host artnj.zte.com.cn
```

#### 3. 配置文件

```bash
# 复制配置模板
cp resource/config/zxtech_config_dev.yaml.template resource/config/zxtech_config_dev.yaml

# 编辑配置文件
vim resource/config/zxtech_config_dev.yaml
```

#### 4. 启动服务

```bash
# 开发模式启动
python main.py

# 生产模式启动
gunicorn -w 4 -b 0.0.0.0:5001 main:app
```

### 方式2: Docker容器部署

#### 1. 构建镜像

```bash
# 构建Docker镜像
docker build -t zte-ibo-acm-productretrieve:latest .

# 查看镜像
docker images | grep zte-ibo-acm-productretrieve
```

#### 2. 运行容器

```bash
# 单容器运行
docker run -d \
  --name zte-qa-service \
  -p 5001:5001 \
  -v /path/to/config:/app/resource/config \
  -v /path/to/logs:/app/logs \
  zte-ibo-acm-productretrieve:latest

# 查看容器状态
docker ps | grep zte-qa-service
```

#### 3. Docker Compose部署

```yaml
# docker-compose.yml
version: '3.8'

services:
  zte-qa-service:
    build: .
    ports:
      - "5001:5001"
    volumes:
      - ./resource/config:/app/resource/config
      - ./logs:/app/logs
    environment:
      - ENV=production
    restart: unless-stopped
    
  redis:
    image: redis:6-alpine
    ports:
      - "6379:6379"
    restart: unless-stopped
```

```bash
# 启动服务栈
docker-compose up -d

# 查看服务状态
docker-compose ps
```

### 方式3: Kubernetes部署

#### 1. 配置文件

```yaml
# k8s-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: zte-qa-service
  labels:
    app: zte-qa-service
spec:
  replicas: 3
  selector:
    matchLabels:
      app: zte-qa-service
  template:
    metadata:
      labels:
        app: zte-qa-service
    spec:
      containers:
      - name: zte-qa-service
        image: zte-ibo-acm-productretrieve:latest
        ports:
        - containerPort: 5001
        env:
        - name: ENV
          value: "production"
        resources:
          requests:
            memory: "2Gi"
            cpu: "1000m"
          limits:
            memory: "4Gi"
            cpu: "2000m"
        livenessProbe:
          httpGet:
            path: /zte-ibo-acm-productretrieve/info
            port: 5001
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /zte-ibo-acm-productretrieve/info
            port: 5001
          initialDelaySeconds: 5
          periodSeconds: 5

---
apiVersion: v1
kind: Service
metadata:
  name: zte-qa-service
spec:
  selector:
    app: zte-qa-service
  ports:
  - protocol: TCP
    port: 80
    targetPort: 5001
  type: LoadBalancer
```

#### 2. 部署到K8s

```bash
# 应用配置
kubectl apply -f k8s-deployment.yaml

# 查看部署状态
kubectl get deployments
kubectl get pods
kubectl get services

# 查看日志
kubectl logs -f deployment/zte-qa-service
```

---

## ⚙️ 配置管理

### 配置文件结构

```yaml
# zxtech_config_dev.yaml
Parameters:
  address: "0.0.0.0"
  port: 5001
  debug: false

LLM:
  url: "https://llm-api.example.com"
  llm_headers:
    Authorization: "encrypted_token"
    Content-Type: "application/json"

Knowledge:
  es_url: "http://elasticsearch:9200"
  access_key: "encrypted_key"
  access_secret: "encrypted_secret"

Embedding:
  url: "http://embedding-service:8080"

Milvus:
  host: "milvus-server"
  port: 19530
  collection_name: "product_vectors"

identify:
  enable: true
  exclude: ["/info", "/health"]
  intercept: ["/faq"]

key-flag: "encryption_key"
```

### 环境变量配置

```bash
# 生产环境变量
export ENV=production
export CONFIG_CENTER_TYPE=apollo
export CONFIG_CENTER_APP_ID=zte-qa-service
export CONFIG_CENTER_URL=http://apollo-config:8080
export CONFIG_CENTER_NAMESPACE=application
export CONFIG_CENTER_CLUSTER=default
export CONFIG_CENTER_SECRET=your_secret
export CONFIG_CENTER_KEY=your_key
```

---

## 📊 监控配置

### 1. 健康检查

```bash
# 服务健康检查
curl -X GET http://localhost:5001/zte-ibo-acm-productretrieve/info

# 预期响应
{"status": "ok"}
```

### 2. 日志监控

```bash
# 查看应用日志
tail -f logs/milvus-$(date +%Y-%m-%d).log

# 日志轮转配置
logrotate -d /etc/logrotate.d/zte-qa-service
```

### 3. 性能监控

```python
# 监控脚本示例
import requests
import time
import json

def monitor_api():
    url = "http://localhost:5001/zte-ibo-acm-productretrieve/faq"
    headers = {
        "Content-Type": "application/json",
        "X-Emp-No": "monitor",
        "X-Auth-Value": "monitor_token"
    }
    data = {"text": "测试问题"}
    
    start_time = time.time()
    try:
        response = requests.post(url, headers=headers, json=data, timeout=30)
        end_time = time.time()
        
        print(f"Status: {response.status_code}")
        print(f"Response Time: {end_time - start_time:.2f}s")
        print(f"Response: {response.json()}")
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    monitor_api()
```

### 4. Prometheus监控

```yaml
# prometheus.yml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'zte-qa-service'
    static_configs:
      - targets: ['localhost:5001']
    metrics_path: '/metrics'
    scrape_interval: 30s
```

---

## 🔧 故障排查

### 常见问题及解决方案

#### 1. 服务启动失败

**问题**: 服务无法启动
```bash
# 检查端口占用
netstat -tlnp | grep 5001

# 检查配置文件
python -c "import yaml; print(yaml.safe_load(open('resource/config/zxtech_config_dev.yaml')))"

# 检查依赖
pip check
```

#### 2. 接口响应超时

**问题**: API请求超时
```bash
# 检查下游服务
curl -X GET http://elasticsearch:9200/_cluster/health
curl -X GET http://milvus:19530/health

# 检查网络连接
ping elasticsearch
ping milvus-server

# 查看应用日志
grep "timeout" logs/*.log
```

#### 3. 内存使用过高

**问题**: 内存占用过高
```bash
# 查看内存使用
free -h
ps aux | grep python | head -10

# 查看进程内存
top -p $(pgrep -f main.py)

# 重启服务
systemctl restart zte-qa-service
```

#### 4. 检索结果异常

**问题**: 检索结果不准确
```bash
# 检查向量数据库
curl -X GET "http://milvus:19530/collections/product_vectors/stats"

# 检查ES索引
curl -X GET "http://elasticsearch:9200/_cat/indices?v"

# 检查模型服务
curl -X POST "http://embedding-service:8080/health"
```

---

## 🔄 运维操作

### 日常维护

```bash
# 1. 日志清理
find logs/ -name "*.log" -mtime +30 -delete

# 2. 缓存清理
redis-cli FLUSHDB

# 3. 重启服务
systemctl restart zte-qa-service

# 4. 备份配置
tar -czf config_backup_$(date +%Y%m%d).tar.gz resource/config/
```

### 扩容操作

```bash
# Kubernetes扩容
kubectl scale deployment zte-qa-service --replicas=5

# Docker Swarm扩容
docker service scale zte-qa-service=5
```

### 版本更新

```bash
# 1. 备份当前版本
docker tag zte-ibo-acm-productretrieve:latest zte-ibo-acm-productretrieve:backup

# 2. 构建新版本
docker build -t zte-ibo-acm-productretrieve:v2.0 .

# 3. 滚动更新
kubectl set image deployment/zte-qa-service zte-qa-service=zte-ibo-acm-productretrieve:v2.0

# 4. 验证更新
kubectl rollout status deployment/zte-qa-service
```

---

## 📞 运维支持

### 联系方式
- **运维团队**: <EMAIL>
- **开发团队**: <EMAIL>
- **紧急联系**: +86-xxx-xxxx-xxxx

### 文档更新
- **维护频率**: 每月更新
- **版本控制**: Git管理
- **审核流程**: 技术评审 → 测试验证 → 发布上线

---

**文档版本**: v1.0  
**最后更新**: 2025-08-01  
**维护团队**: ZTE运维团队
