# ZTE产品智能问答系统 - 文档中心

## 📚 文档概述

欢迎来到ZTE产品智能问答系统的文档中心！本目录包含了系统的完整技术文档，帮助开发者、运维人员和用户全面了解和使用本系统。

---

## 📖 文档目录

### 🏗️ [项目架构分析报告](./项目架构分析报告.md)
**适用人群**: 技术架构师、开发团队负责人、项目经理

**内容概要**:
- 系统整体架构设计
- 核心组件和技术栈分析
- 业务流程详解
- 安全特性和性能优化
- 扩展性设计原则

**关键亮点**:
- AgenticRAG架构深度解析
- 多源检索融合策略
- 企业级安全和监控方案

---

### 📊 [系统架构图表文档](./系统架构图表文档.md)
**适用人群**: 所有技术人员、产品经理、业务分析师

**内容概要**:
- 整体系统架构图
- 项目目录结构图
- 核心业务处理流程图
- 技术栈和依赖关系图
- 数据流向时序图

**关键亮点**:
- 直观的Mermaid图表展示
- 完整的数据流向分析
- 清晰的模块依赖关系

---

### 🌐 [API接口文档](./API接口文档.md)
**适用人群**: 前端开发者、接口调用方、测试工程师

**内容概要**:
- 完整的REST API规范
- 请求/响应格式详解
- 身份验证机制
- 错误处理和状态码
- 业务场景示例

**关键亮点**:
- 详细的接口调用示例
- 多轮对话场景演示
- 性能优化建议

---

### 🚀 [部署运维指南](./部署运维指南.md)
**适用人群**: 运维工程师、DevOps团队、系统管理员

**内容概要**:
- 环境要求和依赖服务
- 多种部署方式（本地/Docker/K8s）
- 配置管理和环境变量
- 监控配置和故障排查
- 日常运维操作

**关键亮点**:
- 完整的容器化部署方案
- 生产级监控配置
- 详细的故障排查手册

---

## 🎯 快速导航

### 👨‍💻 开发人员
1. 首先阅读 [项目架构分析报告](./项目架构分析报告.md) 了解系统整体设计
2. 查看 [系统架构图表文档](./系统架构图表文档.md) 理解模块关系
3. 参考 [API接口文档](./API接口文档.md) 进行接口开发

### 🔧 运维人员
1. 查看 [部署运维指南](./部署运维指南.md) 了解部署要求
2. 参考 [系统架构图表文档](./系统架构图表文档.md) 理解系统架构
3. 使用 [项目架构分析报告](./项目架构分析报告.md) 中的监控方案

### 📋 产品经理
1. 阅读 [项目架构分析报告](./项目架构分析报告.md) 了解技术能力
2. 查看 [API接口文档](./API接口文档.md) 理解功能特性
3. 参考 [系统架构图表文档](./系统架构图表文档.md) 了解业务流程

### 🧪 测试工程师
1. 重点关注 [API接口文档](./API接口文档.md) 的接口规范
2. 参考 [部署运维指南](./部署运维指南.md) 搭建测试环境
3. 使用 [系统架构图表文档](./系统架构图表文档.md) 理解测试场景

---

## 🔍 系统特性概览

### 🎯 核心功能
- **智能问答**: 基于AgenticRAG架构的产品技术问答
- **多轮对话**: 支持上下文相关的连续对话
- **多语言支持**: 中英文查询和回答
- **版本特定**: 针对特定产品版本的精确查询

### 🏗️ 技术架构
- **多源检索**: Elasticsearch + Milvus + 知识图谱
- **智能重排序**: BGE-M3模型优化结果
- **向量化处理**: BGE-M3向量化和语义检索
- **大模型生成**: LLM生成高质量答案

### 🔐 企业特性
- **身份验证**: 完整的Token验证机制
- **配置中心**: Apollo配置管理
- **监控告警**: SkyWalking链路追踪
- **容器化**: Docker和Kubernetes支持

### 📊 性能优化
- **并行检索**: 多源检索并行执行
- **结果缓存**: 智能缓存机制
- **连接池**: 数据库连接池管理
- **负载均衡**: 支持水平扩展

---

## 📋 技术栈总览

| 分类 | 技术组件 | 版本 | 用途 |
|------|----------|------|------|
| **Web框架** | Flask | 2.3.3 | REST API服务 |
| **向量数据库** | Milvus | 2.4.1 | 语义检索 |
| **搜索引擎** | Elasticsearch | 7.x+ | 文本检索 |
| **AI模型** | BGE-M3 | - | 向量化+重排序 |
| **数据处理** | Pandas/NumPy | 2.0.3/1.26.4 | 数据处理 |
| **配置中心** | Apollo | 1.x+ | 配置管理 |
| **监控** | SkyWalking | 1.1.0 | 链路追踪 |
| **容器化** | Docker | 20.x+ | 容器部署 |

---

## 🚀 快速开始

### 1. 环境准备
```bash
# 克隆项目
git clone <repository-url>
cd zte-ibo-acm-productretrieve

# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/Mac
```

### 2. 安装依赖
```bash
pip install -r requirements.txt \
  --index-url https://artnj.zte.com.cn/artifactory/api/pypi/public-pypi-virtual/simple \
  --trusted-host artnj.zte.com.cn
```

### 3. 配置服务
```bash
# 复制配置文件
cp resource/config/zxtech_config_dev.yaml.template resource/config/zxtech_config_dev.yaml

# 编辑配置（根据实际环境修改）
vim resource/config/zxtech_config_dev.yaml
```

### 4. 启动服务
```bash
# 开发模式
python main.py

# 生产模式
gunicorn -w 4 -b 0.0.0.0:5001 main:app
```

### 5. 验证服务
```bash
# 健康检查
curl http://localhost:5001/zte-ibo-acm-productretrieve/info

# 测试问答接口
curl -X POST http://localhost:5001/zte-ibo-acm-productretrieve/faq \
  -H "Content-Type: application/json" \
  -H "X-Emp-No: test" \
  -H "X-Auth-Value: test-token" \
  -d '{"text": "测试问题"}'
```

---

## 📞 支持与联系

### 技术支持
- **开发团队**: <EMAIL>
- **运维团队**: <EMAIL>
- **技术支持**: <EMAIL>

### 文档维护
- **维护团队**: ZTE AI团队
- **更新频率**: 每月更新
- **版本控制**: Git管理
- **问题反馈**: 通过Issue或邮件反馈

### 相关链接
- **项目仓库**: [内部Git仓库]
- **配置中心**: [Apollo配置中心]
- **监控平台**: [SkyWalking监控]
- **知识库**: [内部技术文档]

---

## 📝 更新日志

### v1.0 (2025-08-01)
- ✅ 完成项目架构分析报告
- ✅ 添加系统架构图表文档
- ✅ 编写API接口文档
- ✅ 完善部署运维指南
- ✅ 创建文档中心总览

### 计划更新
- 🔄 添加开发者指南
- 🔄 补充测试用例文档
- 🔄 增加性能调优指南
- 🔄 完善故障排查手册

---

**文档版本**: v1.0  
**最后更新**: 2025-08-01  
**维护团队**: ZTE AI团队  
**文档状态**: ✅ 完整 | 🔄 持续更新
