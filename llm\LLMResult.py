import json
import re
import time

import requests
from flask import Response
from domain.constants.Enums import CONST, Targets, StringType_Response, StringType, Hints
from utils.logger.log_wrapper import get_extra, wrap_log_content
from utils.logger.logger_util import logger
from utils.stream_entity import SSEClient
from prompt.PromptLoader import Pro<PERSON><PERSON>oa<PERSON>
from retrieve.Recall_data import <PERSON>cal<PERSON><PERSON><PERSON><PERSON>
from domain.config.zxtech_config import config
from utils.utils import deal_candidates_by_means
from embedding.get_embedding import GetEmbedding
from domain.constants.Enums import SOURCE
from utils.llm_util.llm_util import judge_query_lang,getUrl_from_infoGo,getId_from_list,getkg_candidate_from_candidates,stream_generate,replace_chinese_brackets,deal_kg_res_length
from utils.llm_util.llm_param import _Stream_Generate_Params_
from utils.llm_util.llm_param import _LLM_Params_
getembedding = GetEmbedding(config)
es_recall = RecallResult(config)


def post_LLM_result(LLM_config, query,LLM_params):
    map_dict = LLM_params.map_dict
    body = LLM_params.body
    doc_res = LLM_params.doc_res
    es_entity = LLM_params.es_entity
    es_res_doc = LLM_params.es_res_doc
    milvus_res_doc = LLM_params.milvus_res_doc
    kg_res = LLM_params.kg_res
    rerank_res_doc = LLM_params.rerank_res_doc
    query_lang = LLM_params.query_lang
    target = LLM_params.target
    history = LLM_params.history
    es_log = '\n' + '%%%%' + '\n' + str(rerank_res_doc) + '\n' + '%%%%' + '\n'
    logger.info(f'[es召回结果]:{es_log}')
    milvus_log = '\n' + '!!!!' + '\n' + str(rerank_res_doc) + '\n' + '!!!!' + '\n'
    logger.info(f'[milvus召回结果]:{milvus_log}')
    rerank_log = '\n' + '$$$$' + '\n' + str(rerank_res_doc) + '\n' + '$$$$' + '\n'
    logger.info(f'[rerank结果]:{rerank_log}')

    if doc_res != Hints.NOT_EXIST.value:
        doc_res = [i['documentID'] for i in next(iter(doc_res.values()))]
    # 候选文本
    candidates = [i['content'] for i in rerank_res_doc]
    res = [i['id'] for i in rerank_res_doc]
    # 候选文本对应的文档
    doc = [i['doc_name'] for i in rerank_res_doc]
    userp=judge_query_lang(query_lang)
    if len(candidates) == 0:
        return Hints.NO_CONTENT.value
    candidates = list(candidates)
    # threshold=0.98
    # embeddings=getembedding.post_url_m3(candidates)
    # candidates=deal_candidates_by_means(candidates,embeddings,threshold)
    s_candidates = str(candidates)
    last_candidate=''
    while len(s_candidates) > 8000 and len(candidates) > 1:
        last_candidate=candidates.pop()
        s_candidates = str(candidates)
    if len(candidates) == 1:
        candidates = [candidates[0][:8000]]
    candidates.append(last_candidate[:8000])
    candidates = candidates[:6]
    doc_effective=doc[:len(candidates)]
    res_effective=res[:len(candidates)]
    ids_final = res[0:len(candidates)]
    kg_res = deal_kg_res_length(kg_res)
    if kg_res:
        doc_effective.insert(0, '_infoGo_')
        res_effective.insert(0, '_infoGo_')
    candidates = kg_res + candidates
    candidates.reverse()
    doc_effective.reverse()
    res_effective.reverse()
    candidates_kg=getkg_candidate_from_candidates(candidates)
    user_content = ""
    candidates_log = ''
    candidates_log += '\n' + "****" + "\n"
    candidates2=[]
    for i, c in enumerate(candidates):
        # c_new = re.sub(r'([*~])', r'\\\1', c)
        c_new = re.sub(r'([*])', r' * ', c)
        user_content += "***"+"\n"+str(i + 1) + "." + c_new+"\n"+ "***"+"\n"
        candidates2.append(str(i + 1) + "." + c_new+"\n"+SOURCE.DOC_FROM.value+doc_effective[i]+ "\n"+ SOURCE.DOC_ID.value + res_effective[i]+"\n")
    candidates_log += user_content
    candidates_log += "****" + "\n"
    # logger.info(f'[给到大模型的切片]:{candidates_log}')
    ids_final = res_effective[0:len(candidates)]
    # ids_final = [i.rsplit("_", 1)[0] for i in ids_final]
    newid_final = getId_from_list(ids_final)
    chunk_id_map = {}
    for i in range(len(newid_final)):
        chunk_id_map[candidates[i]] = newid_final[i]
    ids_final = newid_final
    candidates_url_match = es_recall.query_must_match(ids_final)
    super_url_list = []
    for i in candidates_url_match:
        super_link = '[TSM：  ' + i['file_name'] + '](' + i['url'] + ')'
        super_url_list.append(super_link)
    kg_parts = re.findall(r'(\{.*?\})(?=\{|\Z)', candidates_kg)
    super_url_list=getUrl_from_infoGo(kg_parts,super_url_list)
    super_url_list = list(set(super_url_list))
    urls_str = '\n\n'.join(super_url_list)

    prompt_dict = PromptLoader(user_content=user_content, question=query, target=target,
                               history=history).get_prompt()

    text = prompt_dict['user_prompt']
    text=replace_chinese_brackets(text)
    logger.info(f'[给到大模型的切片]:{text}')
    sys_prompt = prompt_dict['sys_prompt']
    bo = {
        "text": text,
        "chatUuid": "",
        "chatName": "",
        "model": LLM_config['name'],
        "temperature": LLM_config['temperature'],
        "top_k": LLM_config['top_k'],
        "top_p": LLM_config['top_p'],
        "stream": True,
        "keep": True,
        "messages": [
            {
                "role": "system",
                "content": userp+sys_prompt
            }
        ]
    }
    headers = LLM_config['llm_headers']
    try:
        receive = requests.post(
            headers=headers,
            json=bo,
            url=LLM_config['url'],
            timeout=CONST.Timeout.value,
            stream=True,
            verify=True
        )
        # result = receive.json()['choices'][0]["message"]['content']

    except requests.exceptions.Timeout:
        return Hints.LLM_TIMEOUT.value
    _stream_generate_params_=_Stream_Generate_Params_(kg_dict=map_dict,chunk_id_map=chunk_id_map,receive=receive,body=body,doc_res=doc_res,es_entity=es_entity,es_res_doc=es_res_doc,milvus_res_doc=milvus_res_doc,kg_res=kg_res,rerank_res_doc=rerank_res_doc,candidates2=candidates2,urls_str=urls_str,env=config['Enviroment'])
    return Response(stream_generate(Stream_Generate_Params=_stream_generate_params_), mimetype='text/event-stream')


def rewrite_with_llm(text, LLM_config, messages, time_record_dict=None, g=None):
    if g is None:
        g = {}
    bo = {
        "messages": messages,
        "temperature": LLM_config['temperature'],
        "top_k": LLM_config['top_k'],
        "top_p": LLM_config['top_p'],
        "model": LLM_config['rewrite_model_name']
    }
    model_type = LLM_config['rewrite_model_type']
    # Started by AICoder, pid:n3144n88268fc6514fd60ac12060e71d1bc4f4d8
    # 根据环境变量选择模型URL和名称
    rewrite_model_url = LLM_config['rewrite_model_url']
    # Ended by AICoder, pid:n3144n88268fc6514fd60ac12060e71d1bc4f4d8
    try:
        before = time.time()
        receive = requests.post(
            json=bo,
            url=rewrite_model_url,
            timeout=30,
            stream=False,
            verify=True
        )
        after = time.time()
        tc = after - before
        # logger.info(f"重写耗时:{tc} s")
        if time_record_dict is not None:
            time_record_dict['rewrite'] = tc
        res_dict = receive.json()
        rewrite_res = parse_llm_response(res_dict, model_type)
        return rewrite_res
    except requests.exceptions.Timeout:
        logger.error(wrap_log_content(f"Timeout rewrite:{bo}  {rewrite_model_url}", g), extra=get_extra({'log_type':'rewrite'}))
        # logger.error(Hints.LLM_TIMEOUT.value)
        raise Exception(Hints.LLM_TIMEOUT)


def parse_llm_response(res_dict, model_type):
    """
    解析LLM响应字典，根据模型类型返回结果。

    :param res_dict: 响应字典
    :param model_type: 模型类型，'openai'或'studio'
    :return: 解析后的结果
    :raises Exception: 如果模型类型不正确，则抛出异常
    """
    result = res_dict['choices'][0]['message']['content']
    return result
