# 测试健康检查接口
Write-Host "=== 测试健康检查接口 ==="
try {
    $response = Invoke-RestMethod -Uri "http://127.0.0.1:7567/zte-ibo-acm-productretrieve/info" -Method GET
    Write-Host "健康检查结果: $($response | ConvertTo-Json)" -ForegroundColor Green
} catch {
    Write-Host "健康检查失败: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""

# 测试问答接口
Write-Host "=== 测试问答接口 ==="
$body = @{
    text = "M6000-8S支持哪些接口"
} | ConvertTo-Json -Compress

$headers = @{
    'X-Emp-No' = '0668001399'
    'X-Auth-Value' = 'a9429deaab5e7bcd66770610617d41a2'
    'Content-Type' = 'application/json'
}

try {
    $response = Invoke-RestMethod -Uri "http://127.0.0.1:7567/zte-ibo-acm-productretrieve/faq" -Method POST -Body $body -Headers $headers
    Write-Host "问答接口结果:" -ForegroundColor Green
    $response | ConvertTo-Json -Depth 5
} catch {
    Write-Host "问答接口失败: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        Write-Host "状态码: $($_.Exception.Response.StatusCode)" -ForegroundColor Yellow
        try {
            $errorContent = $_.Exception.Response.GetResponseStream()
            $reader = New-Object System.IO.StreamReader($errorContent)
            $errorBody = $reader.ReadToEnd()
            Write-Host "错误响应内容: $errorBody" -ForegroundColor Yellow
        } catch {
            Write-Host "无法读取错误响应内容" -ForegroundColor Yellow
        }
    }
}