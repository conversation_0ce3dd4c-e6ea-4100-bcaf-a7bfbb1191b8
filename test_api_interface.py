#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ZTE IBO ACM 产品检索服务接口测试脚本
创建时间: 2025-01-31 14:30:00
"""

import requests
import json
import time
from datetime import datetime
import sys
import traceback

class APITester:
    def __init__(self, base_url="http://localhost:7567"):
        """
        初始化API测试器
        Args:
            base_url: 服务器基础URL，默认为localhost:7567
        """
        self.base_url = base_url
        self.session = requests.Session()
        self.test_results = []
        
        # 测试用的请求头
        self.headers = {
            "Content-Type": "application/json",
            "X-Emp-No": "10345316",  # 测试工号
            "X-Auth-Value": "test_token_123"  # 测试token
        }
        
    def log_result(self, test_name, success, message, response_time=None, status_code=None):
        """记录测试结果"""
        result = {
            "test_name": test_name,
            "success": success,
            "message": message,
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "response_time": response_time,
            "status_code": status_code
        }
        self.test_results.append(result)
        
        # 打印结果
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name}: {message}")
        if response_time:
            print(f"   响应时间: {response_time:.3f}s")
        if status_code:
            print(f"   状态码: {status_code}")
        print()

    def test_server_connectivity(self):
        """测试服务器连接性"""
        try:
            start_time = time.time()
            response = requests.get(f"{self.base_url}/zte-ibo-acm-productretrieve/info", 
                                  timeout=10)
            response_time = time.time() - start_time
            
            if response.status_code == 200:
                self.log_result("服务器连接测试", True, 
                              "服务器正常响应", response_time, response.status_code)
                return True
            else:
                self.log_result("服务器连接测试", False, 
                              f"服务器响应异常，状态码: {response.status_code}", 
                              response_time, response.status_code)
                return False
        except Exception as e:
            self.log_result("服务器连接测试", False, f"连接失败: {str(e)}")
            return False

    def test_info_endpoint(self):
        """测试/info接口"""
        try:
            # GET请求测试
            start_time = time.time()
            response = requests.get(f"{self.base_url}/zte-ibo-acm-productretrieve/info")
            response_time = time.time() - start_time
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    if data.get("status") == "ok":
                        self.log_result("INFO接口GET测试", True, 
                                      "接口正常返回status=ok", response_time, response.status_code)
                    else:
                        self.log_result("INFO接口GET测试", False, 
                                      f"返回数据异常: {data}", response_time, response.status_code)
                except:
                    self.log_result("INFO接口GET测试", False, 
                                  f"响应不是有效JSON: {response.text}", response_time, response.status_code)
            else:
                self.log_result("INFO接口GET测试", False, 
                              f"状态码异常: {response.status_code}", response_time, response.status_code)
                
            # POST请求测试
            start_time = time.time()
            response = requests.post(f"{self.base_url}/zte-ibo-acm-productretrieve/info")
            response_time = time.time() - start_time
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    if data.get("status") == "ok":
                        self.log_result("INFO接口POST测试", True, 
                                      "接口正常返回status=ok", response_time, response.status_code)
                    else:
                        self.log_result("INFO接口POST测试", False, 
                                      f"返回数据异常: {data}", response_time, response.status_code)
                except:
                    self.log_result("INFO接口POST测试", False, 
                                  f"响应不是有效JSON: {response.text}", response_time, response.status_code)
            else:
                self.log_result("INFO接口POST测试", False, 
                              f"状态码异常: {response.status_code}", response_time, response.status_code)
                
        except Exception as e:
            self.log_result("INFO接口测试", False, f"测试异常: {str(e)}")

    def test_faq_endpoint_auth(self):
        """测试FAQ接口认证机制"""
        
        # 测试缺少工号的情况
        try:
            start_time = time.time()
            headers_no_emp = {"Content-Type": "application/json"}
            data = {"text": "测试问题"}
            
            response = requests.post(f"{self.base_url}/zte-ibo-acm-productretrieve/faq",
                                   headers=headers_no_emp,
                                   json=data)
            response_time = time.time() - start_time
            
            # 期望返回错误，因为缺少X-Emp-No
            if response.status_code != 200:
                self.log_result("FAQ接口认证测试-缺少工号", True, 
                              "正确拒绝了缺少工号的请求", response_time, response.status_code)
            else:
                self.log_result("FAQ接口认证测试-缺少工号", False, 
                              "应该拒绝缺少工号的请求但却通过了", response_time, response.status_code)
                
        except Exception as e:
            self.log_result("FAQ接口认证测试-缺少工号", False, f"测试异常: {str(e)}")

    def test_faq_endpoint_params(self):
        """测试FAQ接口参数验证"""
        
        # 测试空文本
        try:
            start_time = time.time()
            data = {"text": ""}
            
            response = requests.post(f"{self.base_url}/zte-ibo-acm-productretrieve/faq",
                                   headers=self.headers,
                                   json=data)
            response_time = time.time() - start_time
            
            if response.status_code == 200:
                try:
                    resp_data = response.json()
                    # 期望返回错误码，因为输入为空
                    if "Empty Input" in str(resp_data) or "0004" in str(resp_data):
                        self.log_result("FAQ接口参数验证-空文本", True, 
                                      "正确处理了空文本输入", response_time, response.status_code)
                    else:
                        self.log_result("FAQ接口参数验证-空文本", False, 
                                      f"空文本处理异常: {resp_data}", response_time, response.status_code)
                except:
                    self.log_result("FAQ接口参数验证-空文本", False, 
                                  f"响应格式异常: {response.text}", response_time, response.status_code)
            else:
                self.log_result("FAQ接口参数验证-空文本", False, 
                              f"状态码异常: {response.status_code}", response_time, response.status_code)
                
        except Exception as e:
            self.log_result("FAQ接口参数验证-空文本", False, f"测试异常: {str(e)}")

    def test_faq_endpoint_business(self):
        """测试FAQ接口业务功能"""
        
        # 测试基本问答功能
        test_questions = [
            "什么是5G技术？",
            "ZTE的核心产品有哪些？",
            "如何配置路由器？"
        ]
        
        for question in test_questions:
            try:
                start_time = time.time()
                data = {
                    "text": question,
                    "history": [],
                    "rewriteText": None,
                    "chatUuid": f"test_{int(time.time())}"
                }
                
                response = requests.post(f"{self.base_url}/zte-ibo-acm-productretrieve/faq",
                                       headers=self.headers,
                                       json=data,
                                       timeout=30)  # 增加超时时间，因为业务逻辑较复杂
                response_time = time.time() - start_time
                
                if response.status_code == 200:
                    try:
                        resp_data = response.json()
                        # 检查是否有正常的业务响应
                        if isinstance(resp_data, dict):
                            self.log_result(f"FAQ业务测试-{question[:10]}...", True, 
                                          f"接口正常响应", response_time, response.status_code)
                        else:
                            self.log_result(f"FAQ业务测试-{question[:10]}...", False, 
                                          f"响应格式异常: {type(resp_data)}", response_time, response.status_code)
                    except:
                        self.log_result(f"FAQ业务测试-{question[:10]}...", False, 
                                      f"响应不是有效JSON: {response.text[:100]}...", response_time, response.status_code)
                else:
                    self.log_result(f"FAQ业务测试-{question[:10]}...", False, 
                                  f"状态码异常: {response.status_code}", response_time, response.status_code)
                    
                # 避免请求过于频繁
                time.sleep(1)
                
            except requests.exceptions.Timeout:
                self.log_result(f"FAQ业务测试-{question[:10]}...", False, "请求超时")
            except Exception as e:
                self.log_result(f"FAQ业务测试-{question[:10]}...", False, f"测试异常: {str(e)}")

    def test_performance_basic(self):
        """基础性能测试"""
        print("开始基础性能测试...")
        
        # 并发请求测试
        import threading
        
        results = []
        def make_request():
            try:
                start_time = time.time()
                response = requests.get(f"{self.base_url}/zte-ibo-acm-productretrieve/info")
                response_time = time.time() - start_time
                results.append({
                    "success": response.status_code == 200,
                    "response_time": response_time
                })
            except:
                results.append({
                    "success": False,
                    "response_time": None
                })
        
        # 创建5个并发请求
        threads = []
        for _ in range(5):
            thread = threading.Thread(target=make_request)
            threads.append(thread)
            thread.start()
        
        # 等待所有线程完成
        for thread in threads:
            thread.join()
        
        # 分析结果
        success_count = sum(1 for r in results if r["success"])
        avg_response_time = sum(r["response_time"] for r in results if r["response_time"]) / len([r for r in results if r["response_time"]])
        
        if success_count == 5:
            self.log_result("并发性能测试", True, 
                          f"5个并发请求全部成功，平均响应时间: {avg_response_time:.3f}s")
        else:
            self.log_result("并发性能测试", False, 
                          f"只有{success_count}/5个请求成功")

    def run_all_tests(self):
        """运行所有测试"""
        print("=" * 60)
        print("ZTE IBO ACM 产品检索服务接口测试开始")
        print("=" * 60)
        print()
        
        # 基础连接测试
        print("1. 基础连接测试")
        print("-" * 30)
        if not self.test_server_connectivity():
            print("❌ 服务器连接失败，停止后续测试")
            return False
        
        # 状态接口测试
        print("2. 状态接口测试")
        print("-" * 30)
        self.test_info_endpoint()
        
        # 认证机制测试
        print("3. 认证机制测试")
        print("-" * 30)
        self.test_faq_endpoint_auth()
        
        # 参数验证测试
        print("4. 参数验证测试")
        print("-" * 30)
        self.test_faq_endpoint_params()
        
        # 业务功能测试
        print("5. 业务功能测试")
        print("-" * 30)
        self.test_faq_endpoint_business()
        
        # 性能测试
        print("6. 基础性能测试")
        print("-" * 30)
        self.test_performance_basic()
        
        return True

    def generate_report(self):
        """生成测试报告"""
        print("\n" + "=" * 60)
        print("测试报告")
        print("=" * 60)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for r in self.test_results if r["success"])
        failed_tests = total_tests - passed_tests
        
        print(f"总测试数: {total_tests}")
        print(f"通过: {passed_tests}")
        print(f"失败: {failed_tests}")
        print(f"通过率: {(passed_tests/total_tests*100):.1f}%")
        print()
        
        if failed_tests > 0:
            print("失败的测试:")
            for result in self.test_results:
                if not result["success"]:
                    print(f"  ❌ {result['test_name']}: {result['message']}")
        
        # 保存详细报告到文件
        report_filename = f"test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_filename, 'w', encoding='utf-8') as f:
            json.dump(self.test_results, f, ensure_ascii=False, indent=2)
        
        print(f"\n详细测试报告已保存到: {report_filename}")
        
        return passed_tests == total_tests

def main():
    """主函数"""
    if len(sys.argv) > 1:
        base_url = sys.argv[1]
    else:
        base_url = "http://localhost:7567"
    
    print(f"测试目标服务器: {base_url}")
    
    tester = APITester(base_url)
    
    try:
        success = tester.run_all_tests()
        all_passed = tester.generate_report()
        
        if all_passed:
            print("\n🎉 所有测试通过！")
            sys.exit(0)
        else:
            print("\n⚠️  存在测试失败，请查看详细报告")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n测试过程中发生异常: {str(e)}")
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()