#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import traceback
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(__file__))

try:
    print("=== 开始调试测试 ===")
    
    # 测试配置导入
    print("1. 导入配置...")
    from domain.config.zxtech_config import config
    print("OK - 配置导入成功")
    
    # 测试服务初始化
    print("2. 初始化服务...")
    from service.zxtech_service import _ZXTECH_
    service = _ZXTECH_(config)
    print("OK - 服务初始化成功")
    
    # 模拟调用
    print("3. 模拟API调用...")
    query = "M6000-8S支持哪些接口"
    XEmpNo = "0668001399"
    history = None
    rewriteText = None
    
    # 创建简单的g对象
    class MockG:
        def __init__(self):
            self.XEmpNo = XEmpNo
            self.query = query
        
        def __dict__(self):
            return {'XEmpNo': self.XEmpNo, 'query': self.query}
    
    g = MockG()
    
    result = service(query, XEmpNo, history, rewriteText, g.__dict__)
    print("OK - API调用成功")
    print("结果:", result)
    
except Exception as e:
    print("ERROR - 发生错误:")
    print(f"错误类型: {type(e).__name__}")
    print(f"错误信息: {str(e)}")
    print("详细堆栈:")
    traceback.print_exc()